import { z } from 'zod'
import { createTRPCRouter, protectedProcedure, publicProcedure } from '../../lib/trpc'

export const journalRouter = createTRPCRouter({
  // Get all journal entries for the authenticated user
  getAll: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(10),
        cursor: z.string().optional(),
        categoryId: z.string().optional(),
        direction: z.string().optional(), // Added for infinite query compatibility
      }).optional().default({})
    )
    .query(async ({ ctx, input }) => {
      const { limit = 10, cursor, categoryId } = input || {}
      
      const entries = await ctx.prisma.journalEntry.findMany({
        where: {
          userId: ctx.session.user.id,
          ...(categoryId && { categoryId }),
        },
        take: limit + 1,
        cursor: cursor ? { id: cursor } : undefined,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          category: true,
        },
      })

      let nextCursor: typeof cursor | undefined = undefined
      if (entries.length > limit) {
        const nextItem = entries.pop()
        nextCursor = nextItem!.id
      }

      return {
        entries,
        nextCursor,
      }
    }),

  // Get a single journal entry by ID
  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const entry = await ctx.prisma.journalEntry.findFirst({
        where: {
          id: input.id,
          userId: ctx.session.user.id,
        },
        include: {
          category: true,
        },
      })

      if (!entry) {
        throw new Error('Journal entry not found')
      }

      return entry
    }),

  // Create a new journal entry
  create: protectedProcedure
    .input(
      z.object({
        title: z.string().min(1).max(200),
        content: z.string().min(1),
        mood: z.string().optional(),
        categoryId: z.string().optional(),
        isPublic: z.boolean().default(false),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.journalEntry.create({
        data: {
          ...input,
          userId: ctx.session.user.id,
        },
        include: {
          category: true,
        },
      })
    }),

  // Update a journal entry
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        title: z.string().min(1).max(200).optional(),
        content: z.string().min(1).optional(),
        mood: z.string().optional(),
        categoryId: z.string().optional(),
        isPublic: z.boolean().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input

      // Verify the entry belongs to the user
      const existingEntry = await ctx.prisma.journalEntry.findFirst({
        where: {
          id,
          userId: ctx.session.user.id,
        },
      })

      if (!existingEntry) {
        throw new Error('Journal entry not found')
      }

      return ctx.prisma.journalEntry.update({
        where: { id },
        data: updateData,
        include: {
          category: true,
        },
      })
    }),

  // Delete a journal entry
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Verify the entry belongs to the user
      const existingEntry = await ctx.prisma.journalEntry.findFirst({
        where: {
          id: input.id,
          userId: ctx.session.user.id,
        },
      })

      if (!existingEntry) {
        throw new Error('Journal entry not found')
      }

      return ctx.prisma.journalEntry.delete({
        where: { id: input.id },
      })
    }),

  // Get public journal entries (for blog-like functionality)
  getPublic: publicProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(10),
        cursor: z.string().optional(),
        direction: z.string().optional(), // Added for infinite query compatibility
      }).optional().default({})
    )
    .query(async ({ ctx, input }) => {
      const { limit = 10, cursor } = input || {}
      
      const entries = await ctx.prisma.journalEntry.findMany({
        where: {
          isPublic: true,
        },
        take: limit + 1,
        cursor: cursor ? { id: cursor } : undefined,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          category: true,
          user: {
            select: {
              name: true,
              image: true,
            },
          },
        },
      })

      let nextCursor: typeof cursor | undefined = undefined
      if (entries.length > limit) {
        const nextItem = entries.pop()
        nextCursor = nextItem!.id
      }

      return {
        entries,
        nextCursor,
      }
    }),
})
