"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/copy-anything";
exports.ids = ["vendor-chunks/copy-anything"];
exports.modules = {

/***/ "(ssr)/./node_modules/copy-anything/dist/index.js":
/*!**************************************************!*\
  !*** ./node_modules/copy-anything/dist/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   copy: () => (/* binding */ copy)\n/* harmony export */ });\n/* harmony import */ var is_what__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! is-what */ \"(ssr)/./node_modules/is-what/dist/index.js\");\n\n\nfunction assignProp(carry, key, newVal, originalObject, includeNonenumerable) {\n  const propType = {}.propertyIsEnumerable.call(originalObject, key) ? \"enumerable\" : \"nonenumerable\";\n  if (propType === \"enumerable\")\n    carry[key] = newVal;\n  if (includeNonenumerable && propType === \"nonenumerable\") {\n    Object.defineProperty(carry, key, {\n      value: newVal,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    });\n  }\n}\nfunction copy(target, options = {}) {\n  if ((0,is_what__WEBPACK_IMPORTED_MODULE_0__.isArray)(target)) {\n    return target.map((item) => copy(item, options));\n  }\n  if (!(0,is_what__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(target)) {\n    return target;\n  }\n  const props = Object.getOwnPropertyNames(target);\n  const symbols = Object.getOwnPropertySymbols(target);\n  return [...props, ...symbols].reduce((carry, key) => {\n    if ((0,is_what__WEBPACK_IMPORTED_MODULE_0__.isArray)(options.props) && !options.props.includes(key)) {\n      return carry;\n    }\n    const val = target[key];\n    const newVal = copy(val, options);\n    assignProp(carry, key, newVal, target, options.nonenumerable);\n    return carry;\n  }, {});\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/copy-anything/dist/index.js\n");

/***/ })

};
;