{"name": "personal-journal-blog", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint", "test": "jest", "test:watch": "jest --watch", "db:seed": "tsx prisma/seed.ts"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@hookform/resolvers": "^5.2.2", "@prisma/client": "^6.16.2", "@tanstack/react-query": "^5.89.0", "@trpc/client": "^11.5.1", "@trpc/next": "^11.5.1", "@trpc/react-query": "^11.5.1", "@trpc/server": "^11.5.1", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "date-fns": "^4.1.0", "next": "15.5.3", "next-auth": "^4.24.11", "prisma": "^6.16.2", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "superjson": "^2.2.2", "zod": "^4.1.9"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.3", "jest": "^30.1.3", "jest-environment-jsdom": "^30.1.2", "tailwindcss": "^4", "tsx": "^4.20.5", "typescript": "^5"}}