import { render, screen } from '@testing-library/react'
import { SessionProvider } from 'next-auth/react'
import Navigation from '../navigation'

// Mock next-auth
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
  signIn: jest.fn(),
  signOut: jest.fn(),
  SessionProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}))

const mockUseSession = require('next-auth/react').useSession

describe('Navigation', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the navigation with title', () => {
    mockUseSession.mockReturnValue({
      data: null,
      status: 'unauthenticated',
    })

    render(
      <SessionProvider session={null}>
        <Navigation />
      </SessionProvider>
    )

    expect(screen.getByText('Personal Journal')).toBeInTheDocument()
  })

  it('shows sign in button when not authenticated', () => {
    mockUseSession.mockReturnValue({
      data: null,
      status: 'unauthenticated',
    })

    render(
      <SessionProvider session={null}>
        <Navigation />
      </SessionProvider>
    )

    expect(screen.getByText('Sign In')).toBeInTheDocument()
  })

  it('shows user menu when authenticated', () => {
    const mockSession = {
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
      },
    }

    mockUseSession.mockReturnValue({
      data: mockSession,
      status: 'authenticated',
    })

    render(
      <SessionProvider session={mockSession}>
        <Navigation />
      </SessionProvider>
    )

    expect(screen.getByText('Dashboard')).toBeInTheDocument()
    expect(screen.getByText('New Entry')).toBeInTheDocument()
    expect(screen.getByText('Test User')).toBeInTheDocument()
    expect(screen.getByText('Sign Out')).toBeInTheDocument()
  })

  it('shows loading state', () => {
    mockUseSession.mockReturnValue({
      data: null,
      status: 'loading',
    })

    render(
      <SessionProvider session={null}>
        <Navigation />
      </SessionProvider>
    )

    expect(screen.getByText('Loading...')).toBeInTheDocument()
  })
})
