# Personal Journal Blog

A modern personal journal blog application built with Next.js, tRPC, and Prisma. This application allows users to create, manage, and share their personal journal entries with a clean, responsive interface.

## Features

- **User Authentication**: Secure login system using NextAuth.js
- **Journal Management**: Create, read, update, and delete journal entries
- **Categories**: Organize entries with customizable categories
- **Mood Tracking**: Track your mood with each journal entry
- **Public/Private Entries**: Choose to make entries public or keep them private
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Type-Safe API**: Built with tRPC for end-to-end type safety
- **Modern UI**: Clean, accessible interface built with Tailwind CSS

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Backend**: tRPC, Prisma ORM
- **Database**: SQLite (development), easily configurable for PostgreSQL/MySQL
- **Authentication**: NextAuth.js
- **Styling**: Tailwind CSS
- **Form Handling**: React Hook Form with Zod validation
- **Testing**: Jest, React Testing Library

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd personal-journal-blog
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

Edit `.env` and add your configuration:
```env
DATABASE_URL="file:./dev.db"
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"
```

4. Set up the database:
```bash
npx prisma generate
npx prisma db push
npm run db:seed
```

5. Start the development server:
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## Demo Credentials

For testing purposes, you can use these demo credentials:
- **Email**: <EMAIL>
- **Password**: password123

## Project Structure

```
src/
├── app/                    # Next.js app directory
├── components/             # Reusable React components
├── lib/                    # Utility libraries and configurations
├── pages/api/              # API routes
└── server/                 # tRPC server and routers
prisma/
├── schema.prisma          # Database schema
└── seed.ts               # Database seeding script
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run db:seed` - Seed the database with sample data

## Key Features Explained

### Authentication
The app uses NextAuth.js with a credentials provider. In production, you'd want to integrate with OAuth providers or implement proper password hashing.

### Database Schema
- **Users**: Store user information
- **JournalEntry**: Main content with title, content, mood, and privacy settings
- **Category**: Organize entries into categories
- **Account/Session**: NextAuth.js session management

### tRPC API
Type-safe API endpoints for:
- Journal CRUD operations
- Category management
- User authentication
- Public entry fetching

### UI Components
- **Navigation**: Responsive navigation with authentication state
- **JournalEntryForm**: Form for creating/editing entries with validation
- **JournalEntryList**: Paginated list with filtering and actions
- **ErrorBoundary**: Graceful error handling

## Deployment

The application can be deployed to any platform that supports Next.js:

1. **Vercel** (recommended): Connect your GitHub repository
2. **Netlify**: Use the Next.js build plugin
3. **Railway/Render**: Deploy with automatic builds

For production, consider:
- Using PostgreSQL instead of SQLite
- Adding proper authentication providers
- Implementing rate limiting
- Adding monitoring and analytics

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is open source and available under the [MIT License](LICENSE).
