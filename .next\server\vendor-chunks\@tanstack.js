"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack";
exports.ids = ["vendor-chunks/@tanstack"];
exports.modules = {

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/focusManager.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusManager: () => (/* binding */ FocusManager),\n/* harmony export */   focusManager: () => (/* binding */ focusManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/focusManager.ts\n\n\nvar FocusManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  #focused;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onFocus) => {\n      if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n        const listener = () => onFocus();\n        window.addEventListener(\"visibilitychange\", listener, false);\n        return () => {\n          window.removeEventListener(\"visibilitychange\", listener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup((focused) => {\n      if (typeof focused === \"boolean\") {\n        this.setFocused(focused);\n      } else {\n        this.onFocus();\n      }\n    });\n  }\n  setFocused(focused) {\n    const changed = this.#focused !== focused;\n    if (changed) {\n      this.#focused = focused;\n      this.onFocus();\n    }\n  }\n  onFocus() {\n    const isFocused = this.isFocused();\n    this.listeners.forEach((listener) => {\n      listener(isFocused);\n    });\n  }\n  isFocused() {\n    if (typeof this.#focused === \"boolean\") {\n      return this.#focused;\n    }\n    return globalThis.document?.visibilityState !== \"hidden\";\n  }\n};\nvar focusManager = new FocusManager();\n\n//# sourceMappingURL=focusManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/hydration.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/hydration.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultShouldDehydrateMutation: () => (/* binding */ defaultShouldDehydrateMutation),\n/* harmony export */   defaultShouldDehydrateQuery: () => (/* binding */ defaultShouldDehydrateQuery),\n/* harmony export */   dehydrate: () => (/* binding */ dehydrate),\n/* harmony export */   hydrate: () => (/* binding */ hydrate)\n/* harmony export */ });\n/* harmony import */ var _thenable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./thenable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js\");\n// src/hydration.ts\n\nfunction defaultTransformerFn(data) {\n  return data;\n}\nfunction dehydrateMutation(mutation) {\n  return {\n    mutationKey: mutation.options.mutationKey,\n    state: mutation.state,\n    ...mutation.options.scope && { scope: mutation.options.scope },\n    ...mutation.meta && { meta: mutation.meta }\n  };\n}\nfunction dehydrateQuery(query, serializeData, shouldRedactErrors) {\n  return {\n    dehydratedAt: Date.now(),\n    state: {\n      ...query.state,\n      ...query.state.data !== void 0 && {\n        data: serializeData(query.state.data)\n      }\n    },\n    queryKey: query.queryKey,\n    queryHash: query.queryHash,\n    ...query.state.status === \"pending\" && {\n      promise: query.promise?.then(serializeData).catch((error) => {\n        if (!shouldRedactErrors(error)) {\n          return Promise.reject(error);\n        }\n        if (true) {\n          console.error(\n            `A query that was dehydrated as pending ended up rejecting. [${query.queryHash}]: ${error}; The error will be redacted in production builds`\n          );\n        }\n        return Promise.reject(new Error(\"redacted\"));\n      })\n    },\n    ...query.meta && { meta: query.meta }\n  };\n}\nfunction defaultShouldDehydrateMutation(mutation) {\n  return mutation.state.isPaused;\n}\nfunction defaultShouldDehydrateQuery(query) {\n  return query.state.status === \"success\";\n}\nfunction defaultShouldRedactErrors(_) {\n  return true;\n}\nfunction dehydrate(client, options = {}) {\n  const filterMutation = options.shouldDehydrateMutation ?? client.getDefaultOptions().dehydrate?.shouldDehydrateMutation ?? defaultShouldDehydrateMutation;\n  const mutations = client.getMutationCache().getAll().flatMap(\n    (mutation) => filterMutation(mutation) ? [dehydrateMutation(mutation)] : []\n  );\n  const filterQuery = options.shouldDehydrateQuery ?? client.getDefaultOptions().dehydrate?.shouldDehydrateQuery ?? defaultShouldDehydrateQuery;\n  const shouldRedactErrors = options.shouldRedactErrors ?? client.getDefaultOptions().dehydrate?.shouldRedactErrors ?? defaultShouldRedactErrors;\n  const serializeData = options.serializeData ?? client.getDefaultOptions().dehydrate?.serializeData ?? defaultTransformerFn;\n  const queries = client.getQueryCache().getAll().flatMap(\n    (query) => filterQuery(query) ? [dehydrateQuery(query, serializeData, shouldRedactErrors)] : []\n  );\n  return { mutations, queries };\n}\nfunction hydrate(client, dehydratedState, options) {\n  if (typeof dehydratedState !== \"object\" || dehydratedState === null) {\n    return;\n  }\n  const mutationCache = client.getMutationCache();\n  const queryCache = client.getQueryCache();\n  const deserializeData = options?.defaultOptions?.deserializeData ?? client.getDefaultOptions().hydrate?.deserializeData ?? defaultTransformerFn;\n  const mutations = dehydratedState.mutations || [];\n  const queries = dehydratedState.queries || [];\n  mutations.forEach(({ state, ...mutationOptions }) => {\n    mutationCache.build(\n      client,\n      {\n        ...client.getDefaultOptions().hydrate?.mutations,\n        ...options?.defaultOptions?.mutations,\n        ...mutationOptions\n      },\n      state\n    );\n  });\n  queries.forEach(\n    ({ queryKey, state, queryHash, meta, promise, dehydratedAt }) => {\n      const syncData = promise ? (0,_thenable_js__WEBPACK_IMPORTED_MODULE_0__.tryResolveSync)(promise) : void 0;\n      const rawData = state.data === void 0 ? syncData?.data : state.data;\n      const data = rawData === void 0 ? rawData : deserializeData(rawData);\n      let query = queryCache.get(queryHash);\n      const existingQueryIsPending = query?.state.status === \"pending\";\n      const existingQueryIsFetching = query?.state.fetchStatus === \"fetching\";\n      if (query) {\n        const hasNewerSyncData = syncData && // We only need this undefined check to handle older dehydration\n        // payloads that might not have dehydratedAt\n        dehydratedAt !== void 0 && dehydratedAt > query.state.dataUpdatedAt;\n        if (state.dataUpdatedAt > query.state.dataUpdatedAt || hasNewerSyncData) {\n          const { fetchStatus: _ignored, ...serializedState } = state;\n          query.setState({\n            ...serializedState,\n            data\n          });\n        }\n      } else {\n        query = queryCache.build(\n          client,\n          {\n            ...client.getDefaultOptions().hydrate?.queries,\n            ...options?.defaultOptions?.queries,\n            queryKey,\n            queryHash,\n            meta\n          },\n          // Reset fetch status to idle to avoid\n          // query being stuck in fetching state upon hydration\n          {\n            ...state,\n            data,\n            fetchStatus: \"idle\",\n            status: data !== void 0 ? \"success\" : state.status\n          }\n        );\n      }\n      if (promise && !existingQueryIsPending && !existingQueryIsFetching && // Only hydrate if dehydration is newer than any existing data,\n      // this is always true for new queries\n      (dehydratedAt === void 0 || dehydratedAt > query.state.dataUpdatedAt)) {\n        void query.fetch(void 0, {\n          // RSC transformed promises are not thenable\n          initialPromise: Promise.resolve(promise).then(deserializeData)\n        });\n      }\n    }\n  );\n}\n\n//# sourceMappingURL=hydration.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL2h5ZHJhdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQytDO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLCtCQUErQjtBQUNsRSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLElBQXFDO0FBQ2pEO0FBQ0EsMkVBQTJFLGdCQUFnQixLQUFLLFFBQVE7QUFDeEc7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTCx1QkFBdUI7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QiwyQkFBMkI7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxPQUFPLHlEQUF5RDtBQUNoRSxpQ0FBaUMsNERBQWM7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsNENBQTRDO0FBQzlEO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBTUU7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxFWU9TSVxcRHJvcGJveFxcUENcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xccGVyc29uYWwtam91cm5hbC1ibG9nXFxub2RlX21vZHVsZXNcXEB0YW5zdGFja1xccXVlcnktY29yZVxcYnVpbGRcXG1vZGVyblxcaHlkcmF0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9oeWRyYXRpb24udHNcbmltcG9ydCB7IHRyeVJlc29sdmVTeW5jIH0gZnJvbSBcIi4vdGhlbmFibGUuanNcIjtcbmZ1bmN0aW9uIGRlZmF1bHRUcmFuc2Zvcm1lckZuKGRhdGEpIHtcbiAgcmV0dXJuIGRhdGE7XG59XG5mdW5jdGlvbiBkZWh5ZHJhdGVNdXRhdGlvbihtdXRhdGlvbikge1xuICByZXR1cm4ge1xuICAgIG11dGF0aW9uS2V5OiBtdXRhdGlvbi5vcHRpb25zLm11dGF0aW9uS2V5LFxuICAgIHN0YXRlOiBtdXRhdGlvbi5zdGF0ZSxcbiAgICAuLi5tdXRhdGlvbi5vcHRpb25zLnNjb3BlICYmIHsgc2NvcGU6IG11dGF0aW9uLm9wdGlvbnMuc2NvcGUgfSxcbiAgICAuLi5tdXRhdGlvbi5tZXRhICYmIHsgbWV0YTogbXV0YXRpb24ubWV0YSB9XG4gIH07XG59XG5mdW5jdGlvbiBkZWh5ZHJhdGVRdWVyeShxdWVyeSwgc2VyaWFsaXplRGF0YSwgc2hvdWxkUmVkYWN0RXJyb3JzKSB7XG4gIHJldHVybiB7XG4gICAgZGVoeWRyYXRlZEF0OiBEYXRlLm5vdygpLFxuICAgIHN0YXRlOiB7XG4gICAgICAuLi5xdWVyeS5zdGF0ZSxcbiAgICAgIC4uLnF1ZXJ5LnN0YXRlLmRhdGEgIT09IHZvaWQgMCAmJiB7XG4gICAgICAgIGRhdGE6IHNlcmlhbGl6ZURhdGEocXVlcnkuc3RhdGUuZGF0YSlcbiAgICAgIH1cbiAgICB9LFxuICAgIHF1ZXJ5S2V5OiBxdWVyeS5xdWVyeUtleSxcbiAgICBxdWVyeUhhc2g6IHF1ZXJ5LnF1ZXJ5SGFzaCxcbiAgICAuLi5xdWVyeS5zdGF0ZS5zdGF0dXMgPT09IFwicGVuZGluZ1wiICYmIHtcbiAgICAgIHByb21pc2U6IHF1ZXJ5LnByb21pc2U/LnRoZW4oc2VyaWFsaXplRGF0YSkuY2F0Y2goKGVycm9yKSA9PiB7XG4gICAgICAgIGlmICghc2hvdWxkUmVkYWN0RXJyb3JzKGVycm9yKSkge1xuICAgICAgICAgIHJldHVybiBQcm9taXNlLnJlamVjdChlcnJvcik7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICBgQSBxdWVyeSB0aGF0IHdhcyBkZWh5ZHJhdGVkIGFzIHBlbmRpbmcgZW5kZWQgdXAgcmVqZWN0aW5nLiBbJHtxdWVyeS5xdWVyeUhhc2h9XTogJHtlcnJvcn07IFRoZSBlcnJvciB3aWxsIGJlIHJlZGFjdGVkIGluIHByb2R1Y3Rpb24gYnVpbGRzYFxuICAgICAgICAgICk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIFByb21pc2UucmVqZWN0KG5ldyBFcnJvcihcInJlZGFjdGVkXCIpKTtcbiAgICAgIH0pXG4gICAgfSxcbiAgICAuLi5xdWVyeS5tZXRhICYmIHsgbWV0YTogcXVlcnkubWV0YSB9XG4gIH07XG59XG5mdW5jdGlvbiBkZWZhdWx0U2hvdWxkRGVoeWRyYXRlTXV0YXRpb24obXV0YXRpb24pIHtcbiAgcmV0dXJuIG11dGF0aW9uLnN0YXRlLmlzUGF1c2VkO1xufVxuZnVuY3Rpb24gZGVmYXVsdFNob3VsZERlaHlkcmF0ZVF1ZXJ5KHF1ZXJ5KSB7XG4gIHJldHVybiBxdWVyeS5zdGF0ZS5zdGF0dXMgPT09IFwic3VjY2Vzc1wiO1xufVxuZnVuY3Rpb24gZGVmYXVsdFNob3VsZFJlZGFjdEVycm9ycyhfKSB7XG4gIHJldHVybiB0cnVlO1xufVxuZnVuY3Rpb24gZGVoeWRyYXRlKGNsaWVudCwgb3B0aW9ucyA9IHt9KSB7XG4gIGNvbnN0IGZpbHRlck11dGF0aW9uID0gb3B0aW9ucy5zaG91bGREZWh5ZHJhdGVNdXRhdGlvbiA/PyBjbGllbnQuZ2V0RGVmYXVsdE9wdGlvbnMoKS5kZWh5ZHJhdGU/LnNob3VsZERlaHlkcmF0ZU11dGF0aW9uID8/IGRlZmF1bHRTaG91bGREZWh5ZHJhdGVNdXRhdGlvbjtcbiAgY29uc3QgbXV0YXRpb25zID0gY2xpZW50LmdldE11dGF0aW9uQ2FjaGUoKS5nZXRBbGwoKS5mbGF0TWFwKFxuICAgIChtdXRhdGlvbikgPT4gZmlsdGVyTXV0YXRpb24obXV0YXRpb24pID8gW2RlaHlkcmF0ZU11dGF0aW9uKG11dGF0aW9uKV0gOiBbXVxuICApO1xuICBjb25zdCBmaWx0ZXJRdWVyeSA9IG9wdGlvbnMuc2hvdWxkRGVoeWRyYXRlUXVlcnkgPz8gY2xpZW50LmdldERlZmF1bHRPcHRpb25zKCkuZGVoeWRyYXRlPy5zaG91bGREZWh5ZHJhdGVRdWVyeSA/PyBkZWZhdWx0U2hvdWxkRGVoeWRyYXRlUXVlcnk7XG4gIGNvbnN0IHNob3VsZFJlZGFjdEVycm9ycyA9IG9wdGlvbnMuc2hvdWxkUmVkYWN0RXJyb3JzID8/IGNsaWVudC5nZXREZWZhdWx0T3B0aW9ucygpLmRlaHlkcmF0ZT8uc2hvdWxkUmVkYWN0RXJyb3JzID8/IGRlZmF1bHRTaG91bGRSZWRhY3RFcnJvcnM7XG4gIGNvbnN0IHNlcmlhbGl6ZURhdGEgPSBvcHRpb25zLnNlcmlhbGl6ZURhdGEgPz8gY2xpZW50LmdldERlZmF1bHRPcHRpb25zKCkuZGVoeWRyYXRlPy5zZXJpYWxpemVEYXRhID8/IGRlZmF1bHRUcmFuc2Zvcm1lckZuO1xuICBjb25zdCBxdWVyaWVzID0gY2xpZW50LmdldFF1ZXJ5Q2FjaGUoKS5nZXRBbGwoKS5mbGF0TWFwKFxuICAgIChxdWVyeSkgPT4gZmlsdGVyUXVlcnkocXVlcnkpID8gW2RlaHlkcmF0ZVF1ZXJ5KHF1ZXJ5LCBzZXJpYWxpemVEYXRhLCBzaG91bGRSZWRhY3RFcnJvcnMpXSA6IFtdXG4gICk7XG4gIHJldHVybiB7IG11dGF0aW9ucywgcXVlcmllcyB9O1xufVxuZnVuY3Rpb24gaHlkcmF0ZShjbGllbnQsIGRlaHlkcmF0ZWRTdGF0ZSwgb3B0aW9ucykge1xuICBpZiAodHlwZW9mIGRlaHlkcmF0ZWRTdGF0ZSAhPT0gXCJvYmplY3RcIiB8fCBkZWh5ZHJhdGVkU3RhdGUgPT09IG51bGwpIHtcbiAgICByZXR1cm47XG4gIH1cbiAgY29uc3QgbXV0YXRpb25DYWNoZSA9IGNsaWVudC5nZXRNdXRhdGlvbkNhY2hlKCk7XG4gIGNvbnN0IHF1ZXJ5Q2FjaGUgPSBjbGllbnQuZ2V0UXVlcnlDYWNoZSgpO1xuICBjb25zdCBkZXNlcmlhbGl6ZURhdGEgPSBvcHRpb25zPy5kZWZhdWx0T3B0aW9ucz8uZGVzZXJpYWxpemVEYXRhID8/IGNsaWVudC5nZXREZWZhdWx0T3B0aW9ucygpLmh5ZHJhdGU/LmRlc2VyaWFsaXplRGF0YSA/PyBkZWZhdWx0VHJhbnNmb3JtZXJGbjtcbiAgY29uc3QgbXV0YXRpb25zID0gZGVoeWRyYXRlZFN0YXRlLm11dGF0aW9ucyB8fCBbXTtcbiAgY29uc3QgcXVlcmllcyA9IGRlaHlkcmF0ZWRTdGF0ZS5xdWVyaWVzIHx8IFtdO1xuICBtdXRhdGlvbnMuZm9yRWFjaCgoeyBzdGF0ZSwgLi4ubXV0YXRpb25PcHRpb25zIH0pID0+IHtcbiAgICBtdXRhdGlvbkNhY2hlLmJ1aWxkKFxuICAgICAgY2xpZW50LFxuICAgICAge1xuICAgICAgICAuLi5jbGllbnQuZ2V0RGVmYXVsdE9wdGlvbnMoKS5oeWRyYXRlPy5tdXRhdGlvbnMsXG4gICAgICAgIC4uLm9wdGlvbnM/LmRlZmF1bHRPcHRpb25zPy5tdXRhdGlvbnMsXG4gICAgICAgIC4uLm11dGF0aW9uT3B0aW9uc1xuICAgICAgfSxcbiAgICAgIHN0YXRlXG4gICAgKTtcbiAgfSk7XG4gIHF1ZXJpZXMuZm9yRWFjaChcbiAgICAoeyBxdWVyeUtleSwgc3RhdGUsIHF1ZXJ5SGFzaCwgbWV0YSwgcHJvbWlzZSwgZGVoeWRyYXRlZEF0IH0pID0+IHtcbiAgICAgIGNvbnN0IHN5bmNEYXRhID0gcHJvbWlzZSA/IHRyeVJlc29sdmVTeW5jKHByb21pc2UpIDogdm9pZCAwO1xuICAgICAgY29uc3QgcmF3RGF0YSA9IHN0YXRlLmRhdGEgPT09IHZvaWQgMCA/IHN5bmNEYXRhPy5kYXRhIDogc3RhdGUuZGF0YTtcbiAgICAgIGNvbnN0IGRhdGEgPSByYXdEYXRhID09PSB2b2lkIDAgPyByYXdEYXRhIDogZGVzZXJpYWxpemVEYXRhKHJhd0RhdGEpO1xuICAgICAgbGV0IHF1ZXJ5ID0gcXVlcnlDYWNoZS5nZXQocXVlcnlIYXNoKTtcbiAgICAgIGNvbnN0IGV4aXN0aW5nUXVlcnlJc1BlbmRpbmcgPSBxdWVyeT8uc3RhdGUuc3RhdHVzID09PSBcInBlbmRpbmdcIjtcbiAgICAgIGNvbnN0IGV4aXN0aW5nUXVlcnlJc0ZldGNoaW5nID0gcXVlcnk/LnN0YXRlLmZldGNoU3RhdHVzID09PSBcImZldGNoaW5nXCI7XG4gICAgICBpZiAocXVlcnkpIHtcbiAgICAgICAgY29uc3QgaGFzTmV3ZXJTeW5jRGF0YSA9IHN5bmNEYXRhICYmIC8vIFdlIG9ubHkgbmVlZCB0aGlzIHVuZGVmaW5lZCBjaGVjayB0byBoYW5kbGUgb2xkZXIgZGVoeWRyYXRpb25cbiAgICAgICAgLy8gcGF5bG9hZHMgdGhhdCBtaWdodCBub3QgaGF2ZSBkZWh5ZHJhdGVkQXRcbiAgICAgICAgZGVoeWRyYXRlZEF0ICE9PSB2b2lkIDAgJiYgZGVoeWRyYXRlZEF0ID4gcXVlcnkuc3RhdGUuZGF0YVVwZGF0ZWRBdDtcbiAgICAgICAgaWYgKHN0YXRlLmRhdGFVcGRhdGVkQXQgPiBxdWVyeS5zdGF0ZS5kYXRhVXBkYXRlZEF0IHx8IGhhc05ld2VyU3luY0RhdGEpIHtcbiAgICAgICAgICBjb25zdCB7IGZldGNoU3RhdHVzOiBfaWdub3JlZCwgLi4uc2VyaWFsaXplZFN0YXRlIH0gPSBzdGF0ZTtcbiAgICAgICAgICBxdWVyeS5zZXRTdGF0ZSh7XG4gICAgICAgICAgICAuLi5zZXJpYWxpemVkU3RhdGUsXG4gICAgICAgICAgICBkYXRhXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHF1ZXJ5ID0gcXVlcnlDYWNoZS5idWlsZChcbiAgICAgICAgICBjbGllbnQsXG4gICAgICAgICAge1xuICAgICAgICAgICAgLi4uY2xpZW50LmdldERlZmF1bHRPcHRpb25zKCkuaHlkcmF0ZT8ucXVlcmllcyxcbiAgICAgICAgICAgIC4uLm9wdGlvbnM/LmRlZmF1bHRPcHRpb25zPy5xdWVyaWVzLFxuICAgICAgICAgICAgcXVlcnlLZXksXG4gICAgICAgICAgICBxdWVyeUhhc2gsXG4gICAgICAgICAgICBtZXRhXG4gICAgICAgICAgfSxcbiAgICAgICAgICAvLyBSZXNldCBmZXRjaCBzdGF0dXMgdG8gaWRsZSB0byBhdm9pZFxuICAgICAgICAgIC8vIHF1ZXJ5IGJlaW5nIHN0dWNrIGluIGZldGNoaW5nIHN0YXRlIHVwb24gaHlkcmF0aW9uXG4gICAgICAgICAge1xuICAgICAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgICAgICBkYXRhLFxuICAgICAgICAgICAgZmV0Y2hTdGF0dXM6IFwiaWRsZVwiLFxuICAgICAgICAgICAgc3RhdHVzOiBkYXRhICE9PSB2b2lkIDAgPyBcInN1Y2Nlc3NcIiA6IHN0YXRlLnN0YXR1c1xuICAgICAgICAgIH1cbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICAgIGlmIChwcm9taXNlICYmICFleGlzdGluZ1F1ZXJ5SXNQZW5kaW5nICYmICFleGlzdGluZ1F1ZXJ5SXNGZXRjaGluZyAmJiAvLyBPbmx5IGh5ZHJhdGUgaWYgZGVoeWRyYXRpb24gaXMgbmV3ZXIgdGhhbiBhbnkgZXhpc3RpbmcgZGF0YSxcbiAgICAgIC8vIHRoaXMgaXMgYWx3YXlzIHRydWUgZm9yIG5ldyBxdWVyaWVzXG4gICAgICAoZGVoeWRyYXRlZEF0ID09PSB2b2lkIDAgfHwgZGVoeWRyYXRlZEF0ID4gcXVlcnkuc3RhdGUuZGF0YVVwZGF0ZWRBdCkpIHtcbiAgICAgICAgdm9pZCBxdWVyeS5mZXRjaCh2b2lkIDAsIHtcbiAgICAgICAgICAvLyBSU0MgdHJhbnNmb3JtZWQgcHJvbWlzZXMgYXJlIG5vdCB0aGVuYWJsZVxuICAgICAgICAgIGluaXRpYWxQcm9taXNlOiBQcm9taXNlLnJlc29sdmUocHJvbWlzZSkudGhlbihkZXNlcmlhbGl6ZURhdGEpXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH1cbiAgKTtcbn1cbmV4cG9ydCB7XG4gIGRlZmF1bHRTaG91bGREZWh5ZHJhdGVNdXRhdGlvbixcbiAgZGVmYXVsdFNob3VsZERlaHlkcmF0ZVF1ZXJ5LFxuICBkZWh5ZHJhdGUsXG4gIGh5ZHJhdGVcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1oeWRyYXRpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/hydration.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasNextPage: () => (/* binding */ hasNextPage),\n/* harmony export */   hasPreviousPage: () => (/* binding */ hasPreviousPage),\n/* harmony export */   infiniteQueryBehavior: () => (/* binding */ infiniteQueryBehavior)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/infiniteQueryBehavior.ts\n\nfunction infiniteQueryBehavior(pages) {\n  return {\n    onFetch: (context, query) => {\n      const options = context.options;\n      const direction = context.fetchOptions?.meta?.fetchMore?.direction;\n      const oldPages = context.state.data?.pages || [];\n      const oldPageParams = context.state.data?.pageParams || [];\n      let result = { pages: [], pageParams: [] };\n      let currentPage = 0;\n      const fetchFn = async () => {\n        let cancelled = false;\n        const addSignalProperty = (object) => {\n          Object.defineProperty(object, \"signal\", {\n            enumerable: true,\n            get: () => {\n              if (context.signal.aborted) {\n                cancelled = true;\n              } else {\n                context.signal.addEventListener(\"abort\", () => {\n                  cancelled = true;\n                });\n              }\n              return context.signal;\n            }\n          });\n        };\n        const queryFn = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureQueryFn)(context.options, context.fetchOptions);\n        const fetchPage = async (data, param, previous) => {\n          if (cancelled) {\n            return Promise.reject();\n          }\n          if (param == null && data.pages.length) {\n            return Promise.resolve(data);\n          }\n          const createQueryFnContext = () => {\n            const queryFnContext2 = {\n              client: context.client,\n              queryKey: context.queryKey,\n              pageParam: param,\n              direction: previous ? \"backward\" : \"forward\",\n              meta: context.options.meta\n            };\n            addSignalProperty(queryFnContext2);\n            return queryFnContext2;\n          };\n          const queryFnContext = createQueryFnContext();\n          const page = await queryFn(queryFnContext);\n          const { maxPages } = context.options;\n          const addTo = previous ? _utils_js__WEBPACK_IMPORTED_MODULE_0__.addToStart : _utils_js__WEBPACK_IMPORTED_MODULE_0__.addToEnd;\n          return {\n            pages: addTo(data.pages, page, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages)\n          };\n        };\n        if (direction && oldPages.length) {\n          const previous = direction === \"backward\";\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n          const oldData = {\n            pages: oldPages,\n            pageParams: oldPageParams\n          };\n          const param = pageParamFn(options, oldData);\n          result = await fetchPage(oldData, param, previous);\n        } else {\n          const remainingPages = pages ?? oldPages.length;\n          do {\n            const param = currentPage === 0 ? oldPageParams[0] ?? options.initialPageParam : getNextPageParam(options, result);\n            if (currentPage > 0 && param == null) {\n              break;\n            }\n            result = await fetchPage(result, param);\n            currentPage++;\n          } while (currentPage < remainingPages);\n        }\n        return result;\n      };\n      if (context.options.persister) {\n        context.fetchFn = () => {\n          return context.options.persister?.(\n            fetchFn,\n            {\n              client: context.client,\n              queryKey: context.queryKey,\n              meta: context.options.meta,\n              signal: context.signal\n            },\n            query\n          );\n        };\n      } else {\n        context.fetchFn = fetchFn;\n      }\n    }\n  };\n}\nfunction getNextPageParam(options, { pages, pageParams }) {\n  const lastIndex = pages.length - 1;\n  return pages.length > 0 ? options.getNextPageParam(\n    pages[lastIndex],\n    pages,\n    pageParams[lastIndex],\n    pageParams\n  ) : void 0;\n}\nfunction getPreviousPageParam(options, { pages, pageParams }) {\n  return pages.length > 0 ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams) : void 0;\n}\nfunction hasNextPage(options, data) {\n  if (!data) return false;\n  return getNextPageParam(options, data) != null;\n}\nfunction hasPreviousPage(options, data) {\n  if (!data || !options.getPreviousPageParam) return false;\n  return getPreviousPageParam(options, data) != null;\n}\n\n//# sourceMappingURL=infiniteQueryBehavior.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryObserver.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/infiniteQueryObserver.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InfiniteQueryObserver: () => (/* binding */ InfiniteQueryObserver)\n/* harmony export */ });\n/* harmony import */ var _queryObserver_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./queryObserver.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js\");\n/* harmony import */ var _infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./infiniteQueryBehavior.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\");\n// src/infiniteQueryObserver.ts\n\n\nvar InfiniteQueryObserver = class extends _queryObserver_js__WEBPACK_IMPORTED_MODULE_0__.QueryObserver {\n  constructor(client, options) {\n    super(client, options);\n  }\n  bindMethods() {\n    super.bindMethods();\n    this.fetchNextPage = this.fetchNextPage.bind(this);\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this);\n  }\n  setOptions(options) {\n    super.setOptions({\n      ...options,\n      behavior: (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_1__.infiniteQueryBehavior)()\n    });\n  }\n  getOptimisticResult(options) {\n    options.behavior = (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_1__.infiniteQueryBehavior)();\n    return super.getOptimisticResult(options);\n  }\n  fetchNextPage(options) {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: \"forward\" }\n      }\n    });\n  }\n  fetchPreviousPage(options) {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: \"backward\" }\n      }\n    });\n  }\n  createResult(query, options) {\n    const { state } = query;\n    const parentResult = super.createResult(query, options);\n    const { isFetching, isRefetching, isError, isRefetchError } = parentResult;\n    const fetchDirection = state.fetchMeta?.fetchMore?.direction;\n    const isFetchNextPageError = isError && fetchDirection === \"forward\";\n    const isFetchingNextPage = isFetching && fetchDirection === \"forward\";\n    const isFetchPreviousPageError = isError && fetchDirection === \"backward\";\n    const isFetchingPreviousPage = isFetching && fetchDirection === \"backward\";\n    const result = {\n      ...parentResult,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_1__.hasNextPage)(options, state.data),\n      hasPreviousPage: (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_1__.hasPreviousPage)(options, state.data),\n      isFetchNextPageError,\n      isFetchingNextPage,\n      isFetchPreviousPageError,\n      isFetchingPreviousPage,\n      isRefetchError: isRefetchError && !isFetchNextPageError && !isFetchPreviousPageError,\n      isRefetching: isRefetching && !isFetchingNextPage && !isFetchingPreviousPage\n    };\n    return result;\n  }\n};\n\n//# sourceMappingURL=infiniteQueryObserver.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js":
/*!********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/mutation.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Mutation: () => (/* binding */ Mutation),\n/* harmony export */   getDefaultState: () => (/* binding */ getDefaultState)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\");\n// src/mutation.ts\n\n\n\nvar Mutation = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n  #client;\n  #observers;\n  #mutationCache;\n  #retryer;\n  constructor(config) {\n    super();\n    this.#client = config.client;\n    this.mutationId = config.mutationId;\n    this.#mutationCache = config.mutationCache;\n    this.#observers = [];\n    this.state = config.state || getDefaultState();\n    this.setOptions(config.options);\n    this.scheduleGc();\n  }\n  setOptions(options) {\n    this.options = options;\n    this.updateGcTime(this.options.gcTime);\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  addObserver(observer) {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer);\n      this.clearGcTimeout();\n      this.#mutationCache.notify({\n        type: \"observerAdded\",\n        mutation: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    this.#observers = this.#observers.filter((x) => x !== observer);\n    this.scheduleGc();\n    this.#mutationCache.notify({\n      type: \"observerRemoved\",\n      mutation: this,\n      observer\n    });\n  }\n  optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === \"pending\") {\n        this.scheduleGc();\n      } else {\n        this.#mutationCache.remove(this);\n      }\n    }\n  }\n  continue() {\n    return this.#retryer?.continue() ?? // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n    this.execute(this.state.variables);\n  }\n  async execute(variables) {\n    const onContinue = () => {\n      this.#dispatch({ type: \"continue\" });\n    };\n    const mutationFnContext = {\n      client: this.#client,\n      meta: this.options.meta,\n      mutationKey: this.options.mutationKey\n    };\n    this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_1__.createRetryer)({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject(new Error(\"No mutationFn found\"));\n        }\n        return this.options.mutationFn(variables, mutationFnContext);\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue,\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n      networkMode: this.options.networkMode,\n      canRun: () => this.#mutationCache.canRun(this)\n    });\n    const restored = this.state.status === \"pending\";\n    const isPaused = !this.#retryer.canStart();\n    try {\n      if (restored) {\n        onContinue();\n      } else {\n        this.#dispatch({ type: \"pending\", variables, isPaused });\n        await this.#mutationCache.config.onMutate?.(\n          variables,\n          this,\n          mutationFnContext\n        );\n        const context = await this.options.onMutate?.(\n          variables,\n          mutationFnContext\n        );\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: \"pending\",\n            context,\n            variables,\n            isPaused\n          });\n        }\n      }\n      const data = await this.#retryer.start();\n      await this.#mutationCache.config.onSuccess?.(\n        data,\n        variables,\n        this.state.context,\n        this,\n        mutationFnContext\n      );\n      await this.options.onSuccess?.(\n        data,\n        variables,\n        this.state.context,\n        mutationFnContext\n      );\n      await this.#mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this,\n        mutationFnContext\n      );\n      await this.options.onSettled?.(\n        data,\n        null,\n        variables,\n        this.state.context,\n        mutationFnContext\n      );\n      this.#dispatch({ type: \"success\", data });\n      return data;\n    } catch (error) {\n      try {\n        await this.#mutationCache.config.onError?.(\n          error,\n          variables,\n          this.state.context,\n          this,\n          mutationFnContext\n        );\n        await this.options.onError?.(\n          error,\n          variables,\n          this.state.context,\n          mutationFnContext\n        );\n        await this.#mutationCache.config.onSettled?.(\n          void 0,\n          error,\n          this.state.variables,\n          this.state.context,\n          this,\n          mutationFnContext\n        );\n        await this.options.onSettled?.(\n          void 0,\n          error,\n          variables,\n          this.state.context,\n          mutationFnContext\n        );\n        throw error;\n      } finally {\n        this.#dispatch({ type: \"error\", error });\n      }\n    } finally {\n      this.#mutationCache.runNext(this);\n    }\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            isPaused: true\n          };\n        case \"continue\":\n          return {\n            ...state,\n            isPaused: false\n          };\n        case \"pending\":\n          return {\n            ...state,\n            context: action.context,\n            data: void 0,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: action.isPaused,\n            status: \"pending\",\n            variables: action.variables,\n            submittedAt: Date.now()\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: \"success\",\n            isPaused: false\n          };\n        case \"error\":\n          return {\n            ...state,\n            data: void 0,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: \"error\"\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.#observers.forEach((observer) => {\n        observer.onMutationUpdate(action);\n      });\n      this.#mutationCache.notify({\n        mutation: this,\n        type: \"updated\",\n        action\n      });\n    });\n  }\n};\nfunction getDefaultState() {\n  return {\n    context: void 0,\n    data: void 0,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: \"idle\",\n    variables: void 0,\n    submittedAt: 0\n  };\n}\n\n//# sourceMappingURL=mutation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/mutationCache.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationCache: () => (/* binding */ MutationCache)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _mutation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutation.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n// src/mutationCache.ts\n\n\n\n\nvar MutationCache = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#mutations = /* @__PURE__ */ new Set();\n    this.#scopes = /* @__PURE__ */ new Map();\n    this.#mutationId = 0;\n  }\n  #mutations;\n  #scopes;\n  #mutationId;\n  build(client, options, state) {\n    const mutation = new _mutation_js__WEBPACK_IMPORTED_MODULE_1__.Mutation({\n      client,\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state\n    });\n    this.add(mutation);\n    return mutation;\n  }\n  add(mutation) {\n    this.#mutations.add(mutation);\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const scopedMutations = this.#scopes.get(scope);\n      if (scopedMutations) {\n        scopedMutations.push(mutation);\n      } else {\n        this.#scopes.set(scope, [mutation]);\n      }\n    }\n    this.notify({ type: \"added\", mutation });\n  }\n  remove(mutation) {\n    if (this.#mutations.delete(mutation)) {\n      const scope = scopeFor(mutation);\n      if (typeof scope === \"string\") {\n        const scopedMutations = this.#scopes.get(scope);\n        if (scopedMutations) {\n          if (scopedMutations.length > 1) {\n            const index = scopedMutations.indexOf(mutation);\n            if (index !== -1) {\n              scopedMutations.splice(index, 1);\n            }\n          } else if (scopedMutations[0] === mutation) {\n            this.#scopes.delete(scope);\n          }\n        }\n      }\n    }\n    this.notify({ type: \"removed\", mutation });\n  }\n  canRun(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const mutationsWithSameScope = this.#scopes.get(scope);\n      const firstPendingMutation = mutationsWithSameScope?.find(\n        (m) => m.state.status === \"pending\"\n      );\n      return !firstPendingMutation || firstPendingMutation === mutation;\n    } else {\n      return true;\n    }\n  }\n  runNext(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const foundMutation = this.#scopes.get(scope)?.find((m) => m !== mutation && m.state.isPaused);\n      return foundMutation?.continue() ?? Promise.resolve();\n    } else {\n      return Promise.resolve();\n    }\n  }\n  clear() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.#mutations.forEach((mutation) => {\n        this.notify({ type: \"removed\", mutation });\n      });\n      this.#mutations.clear();\n      this.#scopes.clear();\n    });\n  }\n  getAll() {\n    return Array.from(this.#mutations);\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (mutation) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(defaultedFilters, mutation)\n    );\n  }\n  findAll(filters = {}) {\n    return this.getAll().filter((mutation) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation));\n  }\n  notify(event) {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  resumePausedMutations() {\n    const pausedMutations = this.getAll().filter((x) => x.state.isPaused);\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(\n      () => Promise.all(\n        pausedMutations.map((mutation) => mutation.continue().catch(_utils_js__WEBPACK_IMPORTED_MODULE_3__.noop))\n      )\n    );\n  }\n};\nfunction scopeFor(mutation) {\n  return mutation.options.scope?.id;\n}\n\n//# sourceMappingURL=mutationCache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationObserver.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/mutationObserver.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationObserver: () => (/* binding */ MutationObserver)\n/* harmony export */ });\n/* harmony import */ var _mutation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mutation.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/mutationObserver.ts\n\n\n\n\nvar MutationObserver = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  #client;\n  #currentResult = void 0;\n  #currentMutation;\n  #mutateOptions;\n  constructor(client, options) {\n    super();\n    this.#client = client;\n    this.setOptions(options);\n    this.bindMethods();\n    this.#updateResult();\n  }\n  bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  }\n  setOptions(options) {\n    const prevOptions = this.options;\n    this.options = this.#client.defaultMutationOptions(options);\n    if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.shallowEqualObjects)(this.options, prevOptions)) {\n      this.#client.getMutationCache().notify({\n        type: \"observerOptionsUpdated\",\n        mutation: this.#currentMutation,\n        observer: this\n      });\n    }\n    if (prevOptions?.mutationKey && this.options.mutationKey && (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.hashKey)(prevOptions.mutationKey) !== (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.hashKey)(this.options.mutationKey)) {\n      this.reset();\n    } else if (this.#currentMutation?.state.status === \"pending\") {\n      this.#currentMutation.setOptions(this.options);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#currentMutation?.removeObserver(this);\n    }\n  }\n  onMutationUpdate(action) {\n    this.#updateResult();\n    this.#notify(action);\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  reset() {\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = void 0;\n    this.#updateResult();\n    this.#notify();\n  }\n  mutate(variables, options) {\n    this.#mutateOptions = options;\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = this.#client.getMutationCache().build(this.#client, this.options);\n    this.#currentMutation.addObserver(this);\n    return this.#currentMutation.execute(variables);\n  }\n  #updateResult() {\n    const state = this.#currentMutation?.state ?? (0,_mutation_js__WEBPACK_IMPORTED_MODULE_2__.getDefaultState)();\n    this.#currentResult = {\n      ...state,\n      isPending: state.status === \"pending\",\n      isSuccess: state.status === \"success\",\n      isError: state.status === \"error\",\n      isIdle: state.status === \"idle\",\n      mutate: this.mutate,\n      reset: this.reset\n    };\n  }\n  #notify(action) {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      if (this.#mutateOptions && this.hasListeners()) {\n        const variables = this.#currentResult.variables;\n        const onMutateResult = this.#currentResult.context;\n        const context = {\n          client: this.#client,\n          meta: this.options.meta,\n          mutationKey: this.options.mutationKey\n        };\n        if (action?.type === \"success\") {\n          this.#mutateOptions.onSuccess?.(\n            action.data,\n            variables,\n            onMutateResult,\n            context\n          );\n          this.#mutateOptions.onSettled?.(\n            action.data,\n            null,\n            variables,\n            onMutateResult,\n            context\n          );\n        } else if (action?.type === \"error\") {\n          this.#mutateOptions.onError?.(\n            action.error,\n            variables,\n            onMutateResult,\n            context\n          );\n          this.#mutateOptions.onSettled?.(\n            void 0,\n            action.error,\n            variables,\n            onMutateResult,\n            context\n          );\n        }\n      }\n      this.listeners.forEach((listener) => {\n        listener(this.#currentResult);\n      });\n    });\n  }\n};\n\n//# sourceMappingURL=mutationObserver.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/notifyManager.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNotifyManager: () => (/* binding */ createNotifyManager),\n/* harmony export */   defaultScheduler: () => (/* binding */ defaultScheduler),\n/* harmony export */   notifyManager: () => (/* binding */ notifyManager)\n/* harmony export */ });\n/* harmony import */ var _timeoutManager_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./timeoutManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/timeoutManager.js\");\n// src/notifyManager.ts\n\nvar defaultScheduler = _timeoutManager_js__WEBPACK_IMPORTED_MODULE_0__.systemSetTimeoutZero;\nfunction createNotifyManager() {\n  let queue = [];\n  let transactions = 0;\n  let notifyFn = (callback) => {\n    callback();\n  };\n  let batchNotifyFn = (callback) => {\n    callback();\n  };\n  let scheduleFn = defaultScheduler;\n  const schedule = (callback) => {\n    if (transactions) {\n      queue.push(callback);\n    } else {\n      scheduleFn(() => {\n        notifyFn(callback);\n      });\n    }\n  };\n  const flush = () => {\n    const originalQueue = queue;\n    queue = [];\n    if (originalQueue.length) {\n      scheduleFn(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback);\n          });\n        });\n      });\n    }\n  };\n  return {\n    batch: (callback) => {\n      let result;\n      transactions++;\n      try {\n        result = callback();\n      } finally {\n        transactions--;\n        if (!transactions) {\n          flush();\n        }\n      }\n      return result;\n    },\n    /**\n     * All calls to the wrapped function will be batched.\n     */\n    batchCalls: (callback) => {\n      return (...args) => {\n        schedule(() => {\n          callback(...args);\n        });\n      };\n    },\n    schedule,\n    /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */\n    setNotifyFunction: (fn) => {\n      notifyFn = fn;\n    },\n    /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */\n    setBatchNotifyFunction: (fn) => {\n      batchNotifyFn = fn;\n    },\n    setScheduler: (fn) => {\n      scheduleFn = fn;\n    }\n  };\n}\nvar notifyManager = createNotifyManager();\n\n//# sourceMappingURL=notifyManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/onlineManager.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnlineManager: () => (/* binding */ OnlineManager),\n/* harmony export */   onlineManager: () => (/* binding */ onlineManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/onlineManager.ts\n\n\nvar OnlineManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  #online = true;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onOnline) => {\n      if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n        const onlineListener = () => onOnline(true);\n        const offlineListener = () => onOnline(false);\n        window.addEventListener(\"online\", onlineListener, false);\n        window.addEventListener(\"offline\", offlineListener, false);\n        return () => {\n          window.removeEventListener(\"online\", onlineListener);\n          window.removeEventListener(\"offline\", offlineListener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup(this.setOnline.bind(this));\n  }\n  setOnline(online) {\n    const changed = this.#online !== online;\n    if (changed) {\n      this.#online = online;\n      this.listeners.forEach((listener) => {\n        listener(online);\n      });\n    }\n  }\n  isOnline() {\n    return this.#online;\n  }\n};\nvar onlineManager = new OnlineManager();\n\n//# sourceMappingURL=onlineManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queriesObserver.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queriesObserver.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueriesObserver: () => (/* binding */ QueriesObserver)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _queryObserver_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./queryObserver.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/queriesObserver.ts\n\n\n\n\nfunction difference(array1, array2) {\n  const excludeSet = new Set(array2);\n  return array1.filter((x) => !excludeSet.has(x));\n}\nfunction replaceAt(array, index, value) {\n  const copy = array.slice(0);\n  copy[index] = value;\n  return copy;\n}\nvar QueriesObserver = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  #client;\n  #result;\n  #queries;\n  #options;\n  #observers;\n  #combinedResult;\n  #lastCombine;\n  #lastResult;\n  #observerMatches = [];\n  constructor(client, queries, options) {\n    super();\n    this.#client = client;\n    this.#options = options;\n    this.#queries = [];\n    this.#observers = [];\n    this.#result = [];\n    this.setQueries(queries);\n  }\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.#observers.forEach((observer) => {\n        observer.subscribe((result) => {\n          this.#onUpdate(observer, result);\n        });\n      });\n    }\n  }\n  onUnsubscribe() {\n    if (!this.listeners.size) {\n      this.destroy();\n    }\n  }\n  destroy() {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.#observers.forEach((observer) => {\n      observer.destroy();\n    });\n  }\n  setQueries(queries, options) {\n    this.#queries = queries;\n    this.#options = options;\n    if (true) {\n      const queryHashes = queries.map(\n        (query) => this.#client.defaultQueryOptions(query).queryHash\n      );\n      if (new Set(queryHashes).size !== queryHashes.length) {\n        console.warn(\n          \"[QueriesObserver]: Duplicate Queries found. This might result in unexpected behavior.\"\n        );\n      }\n    }\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_1__.notifyManager.batch(() => {\n      const prevObservers = this.#observers;\n      const newObserverMatches = this.#findMatchingObservers(this.#queries);\n      this.#observerMatches = newObserverMatches;\n      newObserverMatches.forEach(\n        (match) => match.observer.setOptions(match.defaultedQueryOptions)\n      );\n      const newObservers = newObserverMatches.map((match) => match.observer);\n      const newResult = newObservers.map(\n        (observer) => observer.getCurrentResult()\n      );\n      const hasLengthChange = prevObservers.length !== newObservers.length;\n      const hasIndexChange = newObservers.some(\n        (observer, index) => observer !== prevObservers[index]\n      );\n      const hasStructuralChange = hasLengthChange || hasIndexChange;\n      const hasResultChange = hasStructuralChange ? true : newResult.some((result, index) => {\n        const prev = this.#result[index];\n        return !prev || !(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.shallowEqualObjects)(result, prev);\n      });\n      if (!hasStructuralChange && !hasResultChange) return;\n      if (hasStructuralChange) {\n        this.#observers = newObservers;\n      }\n      this.#result = newResult;\n      if (!this.hasListeners()) return;\n      if (hasStructuralChange) {\n        difference(prevObservers, newObservers).forEach((observer) => {\n          observer.destroy();\n        });\n        difference(newObservers, prevObservers).forEach((observer) => {\n          observer.subscribe((result) => {\n            this.#onUpdate(observer, result);\n          });\n        });\n      }\n      this.#notify();\n    });\n  }\n  getCurrentResult() {\n    return this.#result;\n  }\n  getQueries() {\n    return this.#observers.map((observer) => observer.getCurrentQuery());\n  }\n  getObservers() {\n    return this.#observers;\n  }\n  getOptimisticResult(queries, combine) {\n    const matches = this.#findMatchingObservers(queries);\n    const result = matches.map(\n      (match) => match.observer.getOptimisticResult(match.defaultedQueryOptions)\n    );\n    return [\n      result,\n      (r) => {\n        return this.#combineResult(r ?? result, combine);\n      },\n      () => {\n        return this.#trackResult(result, matches);\n      }\n    ];\n  }\n  #trackResult(result, matches) {\n    return matches.map((match, index) => {\n      const observerResult = result[index];\n      return !match.defaultedQueryOptions.notifyOnChangeProps ? match.observer.trackResult(observerResult, (accessedProp) => {\n        matches.forEach((m) => {\n          m.observer.trackProp(accessedProp);\n        });\n      }) : observerResult;\n    });\n  }\n  #combineResult(input, combine) {\n    if (combine) {\n      if (!this.#combinedResult || this.#result !== this.#lastResult || combine !== this.#lastCombine) {\n        this.#lastCombine = combine;\n        this.#lastResult = this.#result;\n        this.#combinedResult = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.replaceEqualDeep)(\n          this.#combinedResult,\n          combine(input)\n        );\n      }\n      return this.#combinedResult;\n    }\n    return input;\n  }\n  #findMatchingObservers(queries) {\n    const prevObserversMap = new Map(\n      this.#observers.map((observer) => [observer.options.queryHash, observer])\n    );\n    const observers = [];\n    queries.forEach((options) => {\n      const defaultedOptions = this.#client.defaultQueryOptions(options);\n      const match = prevObserversMap.get(defaultedOptions.queryHash);\n      if (match) {\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer: match\n        });\n      } else {\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer: new _queryObserver_js__WEBPACK_IMPORTED_MODULE_3__.QueryObserver(this.#client, defaultedOptions)\n        });\n      }\n    });\n    return observers;\n  }\n  #onUpdate(observer, result) {\n    const index = this.#observers.indexOf(observer);\n    if (index !== -1) {\n      this.#result = replaceAt(this.#result, index, result);\n      this.#notify();\n    }\n  }\n  #notify() {\n    if (this.hasListeners()) {\n      const previousResult = this.#combinedResult;\n      const newTracked = this.#trackResult(this.#result, this.#observerMatches);\n      const newResult = this.#combineResult(newTracked, this.#options?.combine);\n      if (previousResult !== newResult) {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_1__.notifyManager.batch(() => {\n          this.listeners.forEach((listener) => {\n            listener(this.#result);\n          });\n        });\n      }\n    }\n  }\n};\n\n//# sourceMappingURL=queriesObserver.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queriesObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/query.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Query: () => (/* binding */ Query),\n/* harmony export */   fetchState: () => (/* binding */ fetchState)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\");\n// src/query.ts\n\n\n\n\nvar Query = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n  #initialState;\n  #revertState;\n  #cache;\n  #client;\n  #retryer;\n  #defaultOptions;\n  #abortSignalConsumed;\n  constructor(config) {\n    super();\n    this.#abortSignalConsumed = false;\n    this.#defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.#client = config.client;\n    this.#cache = this.#client.getQueryCache();\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.#initialState = getDefaultState(this.options);\n    this.state = config.state ?? this.#initialState;\n    this.scheduleGc();\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  get promise() {\n    return this.#retryer?.promise;\n  }\n  setOptions(options) {\n    this.options = { ...this.#defaultOptions, ...options };\n    this.updateGcTime(this.options.gcTime);\n    if (this.state && this.state.data === void 0) {\n      const defaultState = getDefaultState(this.options);\n      if (defaultState.data !== void 0) {\n        this.setData(defaultState.data, {\n          updatedAt: defaultState.dataUpdatedAt,\n          manual: true\n        });\n        this.#initialState = defaultState;\n      }\n    }\n  }\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n      this.#cache.remove(this);\n    }\n  }\n  setData(newData, options) {\n    const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceData)(this.state.data, newData, this.options);\n    this.#dispatch({\n      data,\n      type: \"success\",\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual\n    });\n    return data;\n  }\n  setState(state, setStateOptions) {\n    this.#dispatch({ type: \"setState\", state, setStateOptions });\n  }\n  cancel(options) {\n    const promise = this.#retryer?.promise;\n    this.#retryer?.cancel(options);\n    return promise ? promise.then(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop) : Promise.resolve();\n  }\n  destroy() {\n    super.destroy();\n    this.cancel({ silent: true });\n  }\n  reset() {\n    this.destroy();\n    this.setState(this.#initialState);\n  }\n  isActive() {\n    return this.observers.some(\n      (observer) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(observer.options.enabled, this) !== false\n    );\n  }\n  isDisabled() {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive();\n    }\n    return this.options.queryFn === _utils_js__WEBPACK_IMPORTED_MODULE_1__.skipToken || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;\n  }\n  isStatic() {\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveStaleTime)(observer.options.staleTime, this) === \"static\"\n      );\n    }\n    return false;\n  }\n  isStale() {\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => observer.getCurrentResult().isStale\n      );\n    }\n    return this.state.data === void 0 || this.state.isInvalidated;\n  }\n  isStaleByTime(staleTime = 0) {\n    if (this.state.data === void 0) {\n      return true;\n    }\n    if (staleTime === \"static\") {\n      return false;\n    }\n    if (this.state.isInvalidated) {\n      return true;\n    }\n    return !(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.timeUntilStale)(this.state.dataUpdatedAt, staleTime);\n  }\n  onFocus() {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  onOnline() {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer);\n      this.clearGcTimeout();\n      this.#cache.notify({ type: \"observerAdded\", query: this, observer });\n    }\n  }\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer);\n      if (!this.observers.length) {\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({ revert: true });\n          } else {\n            this.#retryer.cancelRetry();\n          }\n        }\n        this.scheduleGc();\n      }\n      this.#cache.notify({ type: \"observerRemoved\", query: this, observer });\n    }\n  }\n  getObserversCount() {\n    return this.observers.length;\n  }\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({ type: \"invalidate\" });\n    }\n  }\n  async fetch(options, fetchOptions) {\n    if (this.state.fetchStatus !== \"idle\" && // If the promise in the retyer is already rejected, we have to definitely\n    // re-start the fetch; there is a chance that the query is still in a\n    // pending state when that happens\n    this.#retryer?.status() !== \"rejected\") {\n      if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n        this.cancel({ silent: true });\n      } else if (this.#retryer) {\n        this.#retryer.continueRetry();\n        return this.#retryer.promise;\n      }\n    }\n    if (options) {\n      this.setOptions(options);\n    }\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn);\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n    if (true) {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`\n        );\n      }\n    }\n    const abortController = new AbortController();\n    const addSignalProperty = (object) => {\n      Object.defineProperty(object, \"signal\", {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true;\n          return abortController.signal;\n        }\n      });\n    };\n    const fetchFn = () => {\n      const queryFn = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureQueryFn)(this.options, fetchOptions);\n      const createQueryFnContext = () => {\n        const queryFnContext2 = {\n          client: this.#client,\n          queryKey: this.queryKey,\n          meta: this.meta\n        };\n        addSignalProperty(queryFnContext2);\n        return queryFnContext2;\n      };\n      const queryFnContext = createQueryFnContext();\n      this.#abortSignalConsumed = false;\n      if (this.options.persister) {\n        return this.options.persister(\n          queryFn,\n          queryFnContext,\n          this\n        );\n      }\n      return queryFn(queryFnContext);\n    };\n    const createFetchContext = () => {\n      const context2 = {\n        fetchOptions,\n        options: this.options,\n        queryKey: this.queryKey,\n        client: this.#client,\n        state: this.state,\n        fetchFn\n      };\n      addSignalProperty(context2);\n      return context2;\n    };\n    const context = createFetchContext();\n    this.options.behavior?.onFetch(context, this);\n    this.#revertState = this.state;\n    if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n      this.#dispatch({ type: \"fetch\", meta: context.fetchOptions?.meta });\n    }\n    this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.createRetryer)({\n      initialPromise: fetchOptions?.initialPromise,\n      fn: context.fetchFn,\n      onCancel: (error) => {\n        if (error instanceof _retryer_js__WEBPACK_IMPORTED_MODULE_2__.CancelledError && error.revert) {\n          this.setState({\n            ...this.#revertState,\n            fetchStatus: \"idle\"\n          });\n        }\n        abortController.abort();\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue: () => {\n        this.#dispatch({ type: \"continue\" });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true\n    });\n    try {\n      const data = await this.#retryer.start();\n      if (data === void 0) {\n        if (true) {\n          console.error(\n            `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`\n          );\n        }\n        throw new Error(`${this.queryHash} data is undefined`);\n      }\n      this.setData(data);\n      this.#cache.config.onSuccess?.(data, this);\n      this.#cache.config.onSettled?.(\n        data,\n        this.state.error,\n        this\n      );\n      return data;\n    } catch (error) {\n      if (error instanceof _retryer_js__WEBPACK_IMPORTED_MODULE_2__.CancelledError) {\n        if (error.silent) {\n          return this.#retryer.promise;\n        } else if (error.revert) {\n          if (this.state.data === void 0) {\n            throw error;\n          }\n          return this.state.data;\n        }\n      }\n      this.#dispatch({\n        type: \"error\",\n        error\n      });\n      this.#cache.config.onError?.(\n        error,\n        this\n      );\n      this.#cache.config.onSettled?.(\n        this.state.data,\n        error,\n        this\n      );\n      throw error;\n    } finally {\n      this.scheduleGc();\n    }\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            fetchStatus: \"paused\"\n          };\n        case \"continue\":\n          return {\n            ...state,\n            fetchStatus: \"fetching\"\n          };\n        case \"fetch\":\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null\n          };\n        case \"success\":\n          const newState = {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: \"success\",\n            ...!action.manual && {\n              fetchStatus: \"idle\",\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            }\n          };\n          this.#revertState = action.manual ? newState : void 0;\n          return newState;\n        case \"error\":\n          const error = action.error;\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: \"idle\",\n            status: \"error\"\n          };\n        case \"invalidate\":\n          return {\n            ...state,\n            isInvalidated: true\n          };\n        case \"setState\":\n          return {\n            ...state,\n            ...action.state\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate();\n      });\n      this.#cache.notify({ query: this, type: \"updated\", action });\n    });\n  }\n};\nfunction fetchState(data, options) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.canFetch)(options.networkMode) ? \"fetching\" : \"paused\",\n    ...data === void 0 && {\n      error: null,\n      status: \"pending\"\n    }\n  };\n}\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n  const hasData = data !== void 0;\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? \"success\" : \"pending\",\n    fetchStatus: \"idle\"\n  };\n}\n\n//# sourceMappingURL=query.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queryCache.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryCache: () => (/* binding */ QueryCache)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./query.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n// src/queryCache.ts\n\n\n\n\nvar QueryCache = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#queries = /* @__PURE__ */ new Map();\n  }\n  #queries;\n  build(client, options, state) {\n    const queryKey = options.queryKey;\n    const queryHash = options.queryHash ?? (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.hashQueryKeyByOptions)(queryKey, options);\n    let query = this.get(queryHash);\n    if (!query) {\n      query = new _query_js__WEBPACK_IMPORTED_MODULE_2__.Query({\n        client,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey)\n      });\n      this.add(query);\n    }\n    return query;\n  }\n  add(query) {\n    if (!this.#queries.has(query.queryHash)) {\n      this.#queries.set(query.queryHash, query);\n      this.notify({\n        type: \"added\",\n        query\n      });\n    }\n  }\n  remove(query) {\n    const queryInMap = this.#queries.get(query.queryHash);\n    if (queryInMap) {\n      query.destroy();\n      if (queryInMap === query) {\n        this.#queries.delete(query.queryHash);\n      }\n      this.notify({ type: \"removed\", query });\n    }\n  }\n  clear() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        this.remove(query);\n      });\n    });\n  }\n  get(queryHash) {\n    return this.#queries.get(queryHash);\n  }\n  getAll() {\n    return [...this.#queries.values()];\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (query) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(defaultedFilters, query)\n    );\n  }\n  findAll(filters = {}) {\n    const queries = this.getAll();\n    return Object.keys(filters).length > 0 ? queries.filter((query) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query)) : queries;\n  }\n  notify(event) {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  onFocus() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onFocus();\n      });\n    });\n  }\n  onOnline() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onOnline();\n      });\n    });\n  }\n};\n\n//# sourceMappingURL=queryCache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3F1ZXJ5Q2FjaGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTtBQUMrRDtBQUM1QjtBQUNnQjtBQUNGO0FBQ2pELCtCQUErQiwwREFBWTtBQUMzQyx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsZ0VBQXFCO0FBQ2hFO0FBQ0E7QUFDQSxrQkFBa0IsNENBQUs7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQix3QkFBd0I7QUFDNUM7QUFDQTtBQUNBO0FBQ0EsSUFBSSw0REFBYTtBQUNqQjtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCO0FBQy9CO0FBQ0EsaUJBQWlCLHFEQUFVO0FBQzNCO0FBQ0E7QUFDQSxzQkFBc0I7QUFDdEI7QUFDQSx1RUFBdUUscURBQVU7QUFDakY7QUFDQTtBQUNBLElBQUksNERBQWE7QUFDakI7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0w7QUFDQTtBQUNBLElBQUksNERBQWE7QUFDakI7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0w7QUFDQTtBQUNBLElBQUksNERBQWE7QUFDakI7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0w7QUFDQTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRVlPU0lcXERyb3Bib3hcXFBDXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXHBlcnNvbmFsLWpvdXJuYWwtYmxvZ1xcbm9kZV9tb2R1bGVzXFxAdGFuc3RhY2tcXHF1ZXJ5LWNvcmVcXGJ1aWxkXFxtb2Rlcm5cXHF1ZXJ5Q2FjaGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3F1ZXJ5Q2FjaGUudHNcbmltcG9ydCB7IGhhc2hRdWVyeUtleUJ5T3B0aW9ucywgbWF0Y2hRdWVyeSB9IGZyb20gXCIuL3V0aWxzLmpzXCI7XG5pbXBvcnQgeyBRdWVyeSB9IGZyb20gXCIuL3F1ZXJ5LmpzXCI7XG5pbXBvcnQgeyBub3RpZnlNYW5hZ2VyIH0gZnJvbSBcIi4vbm90aWZ5TWFuYWdlci5qc1wiO1xuaW1wb3J0IHsgU3Vic2NyaWJhYmxlIH0gZnJvbSBcIi4vc3Vic2NyaWJhYmxlLmpzXCI7XG52YXIgUXVlcnlDYWNoZSA9IGNsYXNzIGV4dGVuZHMgU3Vic2NyaWJhYmxlIHtcbiAgY29uc3RydWN0b3IoY29uZmlnID0ge30pIHtcbiAgICBzdXBlcigpO1xuICAgIHRoaXMuY29uZmlnID0gY29uZmlnO1xuICAgIHRoaXMuI3F1ZXJpZXMgPSAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpO1xuICB9XG4gICNxdWVyaWVzO1xuICBidWlsZChjbGllbnQsIG9wdGlvbnMsIHN0YXRlKSB7XG4gICAgY29uc3QgcXVlcnlLZXkgPSBvcHRpb25zLnF1ZXJ5S2V5O1xuICAgIGNvbnN0IHF1ZXJ5SGFzaCA9IG9wdGlvbnMucXVlcnlIYXNoID8/IGhhc2hRdWVyeUtleUJ5T3B0aW9ucyhxdWVyeUtleSwgb3B0aW9ucyk7XG4gICAgbGV0IHF1ZXJ5ID0gdGhpcy5nZXQocXVlcnlIYXNoKTtcbiAgICBpZiAoIXF1ZXJ5KSB7XG4gICAgICBxdWVyeSA9IG5ldyBRdWVyeSh7XG4gICAgICAgIGNsaWVudCxcbiAgICAgICAgcXVlcnlLZXksXG4gICAgICAgIHF1ZXJ5SGFzaCxcbiAgICAgICAgb3B0aW9uczogY2xpZW50LmRlZmF1bHRRdWVyeU9wdGlvbnMob3B0aW9ucyksXG4gICAgICAgIHN0YXRlLFxuICAgICAgICBkZWZhdWx0T3B0aW9uczogY2xpZW50LmdldFF1ZXJ5RGVmYXVsdHMocXVlcnlLZXkpXG4gICAgICB9KTtcbiAgICAgIHRoaXMuYWRkKHF1ZXJ5KTtcbiAgICB9XG4gICAgcmV0dXJuIHF1ZXJ5O1xuICB9XG4gIGFkZChxdWVyeSkge1xuICAgIGlmICghdGhpcy4jcXVlcmllcy5oYXMocXVlcnkucXVlcnlIYXNoKSkge1xuICAgICAgdGhpcy4jcXVlcmllcy5zZXQocXVlcnkucXVlcnlIYXNoLCBxdWVyeSk7XG4gICAgICB0aGlzLm5vdGlmeSh7XG4gICAgICAgIHR5cGU6IFwiYWRkZWRcIixcbiAgICAgICAgcXVlcnlcbiAgICAgIH0pO1xuICAgIH1cbiAgfVxuICByZW1vdmUocXVlcnkpIHtcbiAgICBjb25zdCBxdWVyeUluTWFwID0gdGhpcy4jcXVlcmllcy5nZXQocXVlcnkucXVlcnlIYXNoKTtcbiAgICBpZiAocXVlcnlJbk1hcCkge1xuICAgICAgcXVlcnkuZGVzdHJveSgpO1xuICAgICAgaWYgKHF1ZXJ5SW5NYXAgPT09IHF1ZXJ5KSB7XG4gICAgICAgIHRoaXMuI3F1ZXJpZXMuZGVsZXRlKHF1ZXJ5LnF1ZXJ5SGFzaCk7XG4gICAgICB9XG4gICAgICB0aGlzLm5vdGlmeSh7IHR5cGU6IFwicmVtb3ZlZFwiLCBxdWVyeSB9KTtcbiAgICB9XG4gIH1cbiAgY2xlYXIoKSB7XG4gICAgbm90aWZ5TWFuYWdlci5iYXRjaCgoKSA9PiB7XG4gICAgICB0aGlzLmdldEFsbCgpLmZvckVhY2goKHF1ZXJ5KSA9PiB7XG4gICAgICAgIHRoaXMucmVtb3ZlKHF1ZXJ5KTtcbiAgICAgIH0pO1xuICAgIH0pO1xuICB9XG4gIGdldChxdWVyeUhhc2gpIHtcbiAgICByZXR1cm4gdGhpcy4jcXVlcmllcy5nZXQocXVlcnlIYXNoKTtcbiAgfVxuICBnZXRBbGwoKSB7XG4gICAgcmV0dXJuIFsuLi50aGlzLiNxdWVyaWVzLnZhbHVlcygpXTtcbiAgfVxuICBmaW5kKGZpbHRlcnMpIHtcbiAgICBjb25zdCBkZWZhdWx0ZWRGaWx0ZXJzID0geyBleGFjdDogdHJ1ZSwgLi4uZmlsdGVycyB9O1xuICAgIHJldHVybiB0aGlzLmdldEFsbCgpLmZpbmQoXG4gICAgICAocXVlcnkpID0+IG1hdGNoUXVlcnkoZGVmYXVsdGVkRmlsdGVycywgcXVlcnkpXG4gICAgKTtcbiAgfVxuICBmaW5kQWxsKGZpbHRlcnMgPSB7fSkge1xuICAgIGNvbnN0IHF1ZXJpZXMgPSB0aGlzLmdldEFsbCgpO1xuICAgIHJldHVybiBPYmplY3Qua2V5cyhmaWx0ZXJzKS5sZW5ndGggPiAwID8gcXVlcmllcy5maWx0ZXIoKHF1ZXJ5KSA9PiBtYXRjaFF1ZXJ5KGZpbHRlcnMsIHF1ZXJ5KSkgOiBxdWVyaWVzO1xuICB9XG4gIG5vdGlmeShldmVudCkge1xuICAgIG5vdGlmeU1hbmFnZXIuYmF0Y2goKCkgPT4ge1xuICAgICAgdGhpcy5saXN0ZW5lcnMuZm9yRWFjaCgobGlzdGVuZXIpID0+IHtcbiAgICAgICAgbGlzdGVuZXIoZXZlbnQpO1xuICAgICAgfSk7XG4gICAgfSk7XG4gIH1cbiAgb25Gb2N1cygpIHtcbiAgICBub3RpZnlNYW5hZ2VyLmJhdGNoKCgpID0+IHtcbiAgICAgIHRoaXMuZ2V0QWxsKCkuZm9yRWFjaCgocXVlcnkpID0+IHtcbiAgICAgICAgcXVlcnkub25Gb2N1cygpO1xuICAgICAgfSk7XG4gICAgfSk7XG4gIH1cbiAgb25PbmxpbmUoKSB7XG4gICAgbm90aWZ5TWFuYWdlci5iYXRjaCgoKSA9PiB7XG4gICAgICB0aGlzLmdldEFsbCgpLmZvckVhY2goKHF1ZXJ5KSA9PiB7XG4gICAgICAgIHF1ZXJ5Lm9uT25saW5lKCk7XG4gICAgICB9KTtcbiAgICB9KTtcbiAgfVxufTtcbmV4cG9ydCB7XG4gIFF1ZXJ5Q2FjaGVcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1xdWVyeUNhY2hlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queryClient.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClient: () => (/* binding */ QueryClient)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _queryCache_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./queryCache.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js\");\n/* harmony import */ var _mutationCache_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutationCache.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js\");\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _onlineManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./onlineManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./infiniteQueryBehavior.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\");\n// src/queryClient.ts\n\n\n\n\n\n\n\nvar QueryClient = class {\n  #queryCache;\n  #mutationCache;\n  #defaultOptions;\n  #queryDefaults;\n  #mutationDefaults;\n  #mountCount;\n  #unsubscribeFocus;\n  #unsubscribeOnline;\n  constructor(config = {}) {\n    this.#queryCache = config.queryCache || new _queryCache_js__WEBPACK_IMPORTED_MODULE_0__.QueryCache();\n    this.#mutationCache = config.mutationCache || new _mutationCache_js__WEBPACK_IMPORTED_MODULE_1__.MutationCache();\n    this.#defaultOptions = config.defaultOptions || {};\n    this.#queryDefaults = /* @__PURE__ */ new Map();\n    this.#mutationDefaults = /* @__PURE__ */ new Map();\n    this.#mountCount = 0;\n  }\n  mount() {\n    this.#mountCount++;\n    if (this.#mountCount !== 1) return;\n    this.#unsubscribeFocus = _focusManager_js__WEBPACK_IMPORTED_MODULE_2__.focusManager.subscribe(async (focused) => {\n      if (focused) {\n        await this.resumePausedMutations();\n        this.#queryCache.onFocus();\n      }\n    });\n    this.#unsubscribeOnline = _onlineManager_js__WEBPACK_IMPORTED_MODULE_3__.onlineManager.subscribe(async (online) => {\n      if (online) {\n        await this.resumePausedMutations();\n        this.#queryCache.onOnline();\n      }\n    });\n  }\n  unmount() {\n    this.#mountCount--;\n    if (this.#mountCount !== 0) return;\n    this.#unsubscribeFocus?.();\n    this.#unsubscribeFocus = void 0;\n    this.#unsubscribeOnline?.();\n    this.#unsubscribeOnline = void 0;\n  }\n  isFetching(filters) {\n    return this.#queryCache.findAll({ ...filters, fetchStatus: \"fetching\" }).length;\n  }\n  isMutating(filters) {\n    return this.#mutationCache.findAll({ ...filters, status: \"pending\" }).length;\n  }\n  /**\n   * Imperative (non-reactive) way to retrieve data for a QueryKey.\n   * Should only be used in callbacks or functions where reading the latest data is necessary, e.g. for optimistic updates.\n   *\n   * Hint: Do not use this function inside a component, because it won't receive updates.\n   * Use `useQuery` to create a `QueryObserver` that subscribes to changes.\n   */\n  getQueryData(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(options.queryHash)?.state.data;\n  }\n  ensureQueryData(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    const query = this.#queryCache.build(this, defaultedOptions);\n    const cachedData = query.state.data;\n    if (cachedData === void 0) {\n      return this.fetchQuery(options);\n    }\n    if (options.revalidateIfStale && query.isStaleByTime((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.resolveStaleTime)(defaultedOptions.staleTime, query))) {\n      void this.prefetchQuery(defaultedOptions);\n    }\n    return Promise.resolve(cachedData);\n  }\n  getQueriesData(filters) {\n    return this.#queryCache.findAll(filters).map(({ queryKey, state }) => {\n      const data = state.data;\n      return [queryKey, data];\n    });\n  }\n  setQueryData(queryKey, updater, options) {\n    const defaultedOptions = this.defaultQueryOptions({ queryKey });\n    const query = this.#queryCache.get(\n      defaultedOptions.queryHash\n    );\n    const prevData = query?.state.data;\n    const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.functionalUpdate)(updater, prevData);\n    if (data === void 0) {\n      return void 0;\n    }\n    return this.#queryCache.build(this, defaultedOptions).setData(data, { ...options, manual: true });\n  }\n  setQueriesData(filters, updater, options) {\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map(({ queryKey }) => [\n        queryKey,\n        this.setQueryData(queryKey, updater, options)\n      ])\n    );\n  }\n  getQueryState(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(\n      options.queryHash\n    )?.state;\n  }\n  removeQueries(filters) {\n    const queryCache = this.#queryCache;\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query);\n      });\n    });\n  }\n  resetQueries(filters, options) {\n    const queryCache = this.#queryCache;\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset();\n      });\n      return this.refetchQueries(\n        {\n          type: \"active\",\n          ...filters\n        },\n        options\n      );\n    });\n  }\n  cancelQueries(filters, cancelOptions = {}) {\n    const defaultedCancelOptions = { revert: true, ...cancelOptions };\n    const promises = _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map((query) => query.cancel(defaultedCancelOptions))\n    );\n    return Promise.all(promises).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  invalidateQueries(filters, options = {}) {\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(() => {\n      this.#queryCache.findAll(filters).forEach((query) => {\n        query.invalidate();\n      });\n      if (filters?.refetchType === \"none\") {\n        return Promise.resolve();\n      }\n      return this.refetchQueries(\n        {\n          ...filters,\n          type: filters?.refetchType ?? filters?.type ?? \"active\"\n        },\n        options\n      );\n    });\n  }\n  refetchQueries(filters, options = {}) {\n    const fetchOptions = {\n      ...options,\n      cancelRefetch: options.cancelRefetch ?? true\n    };\n    const promises = _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(\n      () => this.#queryCache.findAll(filters).filter((query) => !query.isDisabled() && !query.isStatic()).map((query) => {\n        let promise = query.fetch(void 0, fetchOptions);\n        if (!fetchOptions.throwOnError) {\n          promise = promise.catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n        }\n        return query.state.fetchStatus === \"paused\" ? Promise.resolve() : promise;\n      })\n    );\n    return Promise.all(promises).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  fetchQuery(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    if (defaultedOptions.retry === void 0) {\n      defaultedOptions.retry = false;\n    }\n    const query = this.#queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.resolveStaleTime)(defaultedOptions.staleTime, query)\n    ) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  }\n  prefetchQuery(options) {\n    return this.fetchQuery(options).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  fetchInfiniteQuery(options) {\n    options.behavior = (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__.infiniteQueryBehavior)(options.pages);\n    return this.fetchQuery(options);\n  }\n  prefetchInfiniteQuery(options) {\n    return this.fetchInfiniteQuery(options).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  ensureInfiniteQueryData(options) {\n    options.behavior = (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__.infiniteQueryBehavior)(options.pages);\n    return this.ensureQueryData(options);\n  }\n  resumePausedMutations() {\n    if (_onlineManager_js__WEBPACK_IMPORTED_MODULE_3__.onlineManager.isOnline()) {\n      return this.#mutationCache.resumePausedMutations();\n    }\n    return Promise.resolve();\n  }\n  getQueryCache() {\n    return this.#queryCache;\n  }\n  getMutationCache() {\n    return this.#mutationCache;\n  }\n  getDefaultOptions() {\n    return this.#defaultOptions;\n  }\n  setDefaultOptions(options) {\n    this.#defaultOptions = options;\n  }\n  setQueryDefaults(queryKey, options) {\n    this.#queryDefaults.set((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashKey)(queryKey), {\n      queryKey,\n      defaultOptions: options\n    });\n  }\n  getQueryDefaults(queryKey) {\n    const defaults = [...this.#queryDefaults.values()];\n    const result = {};\n    defaults.forEach((queryDefault) => {\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.partialMatchKey)(queryKey, queryDefault.queryKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  setMutationDefaults(mutationKey, options) {\n    this.#mutationDefaults.set((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashKey)(mutationKey), {\n      mutationKey,\n      defaultOptions: options\n    });\n  }\n  getMutationDefaults(mutationKey) {\n    const defaults = [...this.#mutationDefaults.values()];\n    const result = {};\n    defaults.forEach((queryDefault) => {\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.partialMatchKey)(mutationKey, queryDefault.mutationKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  defaultQueryOptions(options) {\n    if (options._defaulted) {\n      return options;\n    }\n    const defaultedOptions = {\n      ...this.#defaultOptions.queries,\n      ...this.getQueryDefaults(options.queryKey),\n      ...options,\n      _defaulted: true\n    };\n    if (!defaultedOptions.queryHash) {\n      defaultedOptions.queryHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashQueryKeyByOptions)(\n        defaultedOptions.queryKey,\n        defaultedOptions\n      );\n    }\n    if (defaultedOptions.refetchOnReconnect === void 0) {\n      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== \"always\";\n    }\n    if (defaultedOptions.throwOnError === void 0) {\n      defaultedOptions.throwOnError = !!defaultedOptions.suspense;\n    }\n    if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n      defaultedOptions.networkMode = \"offlineFirst\";\n    }\n    if (defaultedOptions.queryFn === _utils_js__WEBPACK_IMPORTED_MODULE_4__.skipToken) {\n      defaultedOptions.enabled = false;\n    }\n    return defaultedOptions;\n  }\n  defaultMutationOptions(options) {\n    if (options?._defaulted) {\n      return options;\n    }\n    return {\n      ...this.#defaultOptions.mutations,\n      ...options?.mutationKey && this.getMutationDefaults(options.mutationKey),\n      ...options,\n      _defaulted: true\n    };\n  }\n  clear() {\n    this.#queryCache.clear();\n    this.#mutationCache.clear();\n  }\n};\n\n//# sourceMappingURL=queryClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queryObserver.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryObserver: () => (/* binding */ QueryObserver)\n/* harmony export */ });\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./query.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _thenable_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./thenable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _timeoutManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./timeoutManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/timeoutManager.js\");\n// src/queryObserver.ts\n\n\n\n\n\n\n\nvar QueryObserver = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(client, options) {\n    super();\n    this.options = options;\n    this.#client = client;\n    this.#selectError = null;\n    this.#currentThenable = (0,_thenable_js__WEBPACK_IMPORTED_MODULE_1__.pendingThenable)();\n    this.bindMethods();\n    this.setOptions(options);\n  }\n  #client;\n  #currentQuery = void 0;\n  #currentQueryInitialState = void 0;\n  #currentResult = void 0;\n  #currentResultState;\n  #currentResultOptions;\n  #currentThenable;\n  #selectError;\n  #selectFn;\n  #selectResult;\n  // This property keeps track of the last query with defined data.\n  // It will be used to pass the previous data and query to the placeholder function between renders.\n  #lastQueryWithDefinedData;\n  #staleTimeoutId;\n  #refetchIntervalId;\n  #currentRefetchInterval;\n  #trackedProps = /* @__PURE__ */ new Set();\n  bindMethods() {\n    this.refetch = this.refetch.bind(this);\n  }\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.#currentQuery.addObserver(this);\n      if (shouldFetchOnMount(this.#currentQuery, this.options)) {\n        this.#executeFetch();\n      } else {\n        this.updateResult();\n      }\n      this.#updateTimers();\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.destroy();\n    }\n  }\n  shouldFetchOnReconnect() {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnReconnect\n    );\n  }\n  shouldFetchOnWindowFocus() {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus\n    );\n  }\n  destroy() {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.#clearStaleTimeout();\n    this.#clearRefetchInterval();\n    this.#currentQuery.removeObserver(this);\n  }\n  setOptions(options) {\n    const prevOptions = this.options;\n    const prevQuery = this.#currentQuery;\n    this.options = this.#client.defaultQueryOptions(options);\n    if (this.options.enabled !== void 0 && typeof this.options.enabled !== \"boolean\" && typeof this.options.enabled !== \"function\" && typeof (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(this.options.enabled, this.#currentQuery) !== \"boolean\") {\n      throw new Error(\n        \"Expected enabled to be a boolean or a callback that returns a boolean\"\n      );\n    }\n    this.#updateQuery();\n    this.#currentQuery.setOptions(this.options);\n    if (prevOptions._defaulted && !(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.shallowEqualObjects)(this.options, prevOptions)) {\n      this.#client.getQueryCache().notify({\n        type: \"observerOptionsUpdated\",\n        query: this.#currentQuery,\n        observer: this\n      });\n    }\n    const mounted = this.hasListeners();\n    if (mounted && shouldFetchOptionally(\n      this.#currentQuery,\n      prevQuery,\n      this.options,\n      prevOptions\n    )) {\n      this.#executeFetch();\n    }\n    this.updateResult();\n    if (mounted && (this.#currentQuery !== prevQuery || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(this.options.enabled, this.#currentQuery) !== (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(prevOptions.enabled, this.#currentQuery) || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveStaleTime)(this.options.staleTime, this.#currentQuery) !== (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveStaleTime)(prevOptions.staleTime, this.#currentQuery))) {\n      this.#updateStaleTimeout();\n    }\n    const nextRefetchInterval = this.#computeRefetchInterval();\n    if (mounted && (this.#currentQuery !== prevQuery || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(this.options.enabled, this.#currentQuery) !== (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(prevOptions.enabled, this.#currentQuery) || nextRefetchInterval !== this.#currentRefetchInterval)) {\n      this.#updateRefetchInterval(nextRefetchInterval);\n    }\n  }\n  getOptimisticResult(options) {\n    const query = this.#client.getQueryCache().build(this.#client, options);\n    const result = this.createResult(query, options);\n    if (shouldAssignObserverCurrentProperties(this, result)) {\n      this.#currentResult = result;\n      this.#currentResultOptions = this.options;\n      this.#currentResultState = this.#currentQuery.state;\n    }\n    return result;\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  trackResult(result, onPropTracked) {\n    return new Proxy(result, {\n      get: (target, key) => {\n        this.trackProp(key);\n        onPropTracked?.(key);\n        if (key === \"promise\" && !this.options.experimental_prefetchInRender && this.#currentThenable.status === \"pending\") {\n          this.#currentThenable.reject(\n            new Error(\n              \"experimental_prefetchInRender feature flag is not enabled\"\n            )\n          );\n        }\n        return Reflect.get(target, key);\n      }\n    });\n  }\n  trackProp(key) {\n    this.#trackedProps.add(key);\n  }\n  getCurrentQuery() {\n    return this.#currentQuery;\n  }\n  refetch({ ...options } = {}) {\n    return this.fetch({\n      ...options\n    });\n  }\n  fetchOptimistic(options) {\n    const defaultedOptions = this.#client.defaultQueryOptions(options);\n    const query = this.#client.getQueryCache().build(this.#client, defaultedOptions);\n    return query.fetch().then(() => this.createResult(query, defaultedOptions));\n  }\n  fetch(fetchOptions) {\n    return this.#executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true\n    }).then(() => {\n      this.updateResult();\n      return this.#currentResult;\n    });\n  }\n  #executeFetch(fetchOptions) {\n    this.#updateQuery();\n    let promise = this.#currentQuery.fetch(\n      this.options,\n      fetchOptions\n    );\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(_utils_js__WEBPACK_IMPORTED_MODULE_2__.noop);\n    }\n    return promise;\n  }\n  #updateStaleTimeout() {\n    this.#clearStaleTimeout();\n    const staleTime = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveStaleTime)(\n      this.options.staleTime,\n      this.#currentQuery\n    );\n    if (_utils_js__WEBPACK_IMPORTED_MODULE_2__.isServer || this.#currentResult.isStale || !(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isValidTimeout)(staleTime)) {\n      return;\n    }\n    const time = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.timeUntilStale)(this.#currentResult.dataUpdatedAt, staleTime);\n    const timeout = time + 1;\n    this.#staleTimeoutId = _timeoutManager_js__WEBPACK_IMPORTED_MODULE_3__.timeoutManager.setTimeout(() => {\n      if (!this.#currentResult.isStale) {\n        this.updateResult();\n      }\n    }, timeout);\n  }\n  #computeRefetchInterval() {\n    return (typeof this.options.refetchInterval === \"function\" ? this.options.refetchInterval(this.#currentQuery) : this.options.refetchInterval) ?? false;\n  }\n  #updateRefetchInterval(nextInterval) {\n    this.#clearRefetchInterval();\n    this.#currentRefetchInterval = nextInterval;\n    if (_utils_js__WEBPACK_IMPORTED_MODULE_2__.isServer || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(this.options.enabled, this.#currentQuery) === false || !(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isValidTimeout)(this.#currentRefetchInterval) || this.#currentRefetchInterval === 0) {\n      return;\n    }\n    this.#refetchIntervalId = _timeoutManager_js__WEBPACK_IMPORTED_MODULE_3__.timeoutManager.setInterval(() => {\n      if (this.options.refetchIntervalInBackground || _focusManager_js__WEBPACK_IMPORTED_MODULE_4__.focusManager.isFocused()) {\n        this.#executeFetch();\n      }\n    }, this.#currentRefetchInterval);\n  }\n  #updateTimers() {\n    this.#updateStaleTimeout();\n    this.#updateRefetchInterval(this.#computeRefetchInterval());\n  }\n  #clearStaleTimeout() {\n    if (this.#staleTimeoutId) {\n      _timeoutManager_js__WEBPACK_IMPORTED_MODULE_3__.timeoutManager.clearTimeout(this.#staleTimeoutId);\n      this.#staleTimeoutId = void 0;\n    }\n  }\n  #clearRefetchInterval() {\n    if (this.#refetchIntervalId) {\n      _timeoutManager_js__WEBPACK_IMPORTED_MODULE_3__.timeoutManager.clearInterval(this.#refetchIntervalId);\n      this.#refetchIntervalId = void 0;\n    }\n  }\n  createResult(query, options) {\n    const prevQuery = this.#currentQuery;\n    const prevOptions = this.options;\n    const prevResult = this.#currentResult;\n    const prevResultState = this.#currentResultState;\n    const prevResultOptions = this.#currentResultOptions;\n    const queryChange = query !== prevQuery;\n    const queryInitialState = queryChange ? query.state : this.#currentQueryInitialState;\n    const { state } = query;\n    let newState = { ...state };\n    let isPlaceholderData = false;\n    let data;\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners();\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      const fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n      if (fetchOnMount || fetchOptionally) {\n        newState = {\n          ...newState,\n          ...(0,_query_js__WEBPACK_IMPORTED_MODULE_5__.fetchState)(state.data, query.options)\n        };\n      }\n      if (options._optimisticResults === \"isRestoring\") {\n        newState.fetchStatus = \"idle\";\n      }\n    }\n    let { error, errorUpdatedAt, status } = newState;\n    data = newState.data;\n    let skipSelect = false;\n    if (options.placeholderData !== void 0 && data === void 0 && status === \"pending\") {\n      let placeholderData;\n      if (prevResult?.isPlaceholderData && options.placeholderData === prevResultOptions?.placeholderData) {\n        placeholderData = prevResult.data;\n        skipSelect = true;\n      } else {\n        placeholderData = typeof options.placeholderData === \"function\" ? options.placeholderData(\n          this.#lastQueryWithDefinedData?.state.data,\n          this.#lastQueryWithDefinedData\n        ) : options.placeholderData;\n      }\n      if (placeholderData !== void 0) {\n        status = \"success\";\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.replaceData)(\n          prevResult?.data,\n          placeholderData,\n          options\n        );\n        isPlaceholderData = true;\n      }\n    }\n    if (options.select && data !== void 0 && !skipSelect) {\n      if (prevResult && data === prevResultState?.data && options.select === this.#selectFn) {\n        data = this.#selectResult;\n      } else {\n        try {\n          this.#selectFn = options.select;\n          data = options.select(data);\n          data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.replaceData)(prevResult?.data, data, options);\n          this.#selectResult = data;\n          this.#selectError = null;\n        } catch (selectError) {\n          this.#selectError = selectError;\n        }\n      }\n    }\n    if (this.#selectError) {\n      error = this.#selectError;\n      data = this.#selectResult;\n      errorUpdatedAt = Date.now();\n      status = \"error\";\n    }\n    const isFetching = newState.fetchStatus === \"fetching\";\n    const isPending = status === \"pending\";\n    const isError = status === \"error\";\n    const isLoading = isPending && isFetching;\n    const hasData = data !== void 0;\n    const result = {\n      status,\n      fetchStatus: newState.fetchStatus,\n      isPending,\n      isSuccess: status === \"success\",\n      isError,\n      isInitialLoading: isLoading,\n      isLoading,\n      data,\n      dataUpdatedAt: newState.dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: newState.fetchFailureCount,\n      failureReason: newState.fetchFailureReason,\n      errorUpdateCount: newState.errorUpdateCount,\n      isFetched: newState.dataUpdateCount > 0 || newState.errorUpdateCount > 0,\n      isFetchedAfterMount: newState.dataUpdateCount > queryInitialState.dataUpdateCount || newState.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isPending,\n      isLoadingError: isError && !hasData,\n      isPaused: newState.fetchStatus === \"paused\",\n      isPlaceholderData,\n      isRefetchError: isError && hasData,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      promise: this.#currentThenable,\n      isEnabled: (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(options.enabled, query) !== false\n    };\n    const nextResult = result;\n    if (this.options.experimental_prefetchInRender) {\n      const finalizeThenableIfPossible = (thenable) => {\n        if (nextResult.status === \"error\") {\n          thenable.reject(nextResult.error);\n        } else if (nextResult.data !== void 0) {\n          thenable.resolve(nextResult.data);\n        }\n      };\n      const recreateThenable = () => {\n        const pending = this.#currentThenable = nextResult.promise = (0,_thenable_js__WEBPACK_IMPORTED_MODULE_1__.pendingThenable)();\n        finalizeThenableIfPossible(pending);\n      };\n      const prevThenable = this.#currentThenable;\n      switch (prevThenable.status) {\n        case \"pending\":\n          if (query.queryHash === prevQuery.queryHash) {\n            finalizeThenableIfPossible(prevThenable);\n          }\n          break;\n        case \"fulfilled\":\n          if (nextResult.status === \"error\" || nextResult.data !== prevThenable.value) {\n            recreateThenable();\n          }\n          break;\n        case \"rejected\":\n          if (nextResult.status !== \"error\" || nextResult.error !== prevThenable.reason) {\n            recreateThenable();\n          }\n          break;\n      }\n    }\n    return nextResult;\n  }\n  updateResult() {\n    const prevResult = this.#currentResult;\n    const nextResult = this.createResult(this.#currentQuery, this.options);\n    this.#currentResultState = this.#currentQuery.state;\n    this.#currentResultOptions = this.options;\n    if (this.#currentResultState.data !== void 0) {\n      this.#lastQueryWithDefinedData = this.#currentQuery;\n    }\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.shallowEqualObjects)(nextResult, prevResult)) {\n      return;\n    }\n    this.#currentResult = nextResult;\n    const shouldNotifyListeners = () => {\n      if (!prevResult) {\n        return true;\n      }\n      const { notifyOnChangeProps } = this.options;\n      const notifyOnChangePropsValue = typeof notifyOnChangeProps === \"function\" ? notifyOnChangeProps() : notifyOnChangeProps;\n      if (notifyOnChangePropsValue === \"all\" || !notifyOnChangePropsValue && !this.#trackedProps.size) {\n        return true;\n      }\n      const includedProps = new Set(\n        notifyOnChangePropsValue ?? this.#trackedProps\n      );\n      if (this.options.throwOnError) {\n        includedProps.add(\"error\");\n      }\n      return Object.keys(this.#currentResult).some((key) => {\n        const typedKey = key;\n        const changed = this.#currentResult[typedKey] !== prevResult[typedKey];\n        return changed && includedProps.has(typedKey);\n      });\n    };\n    this.#notify({ listeners: shouldNotifyListeners() });\n  }\n  #updateQuery() {\n    const query = this.#client.getQueryCache().build(this.#client, this.options);\n    if (query === this.#currentQuery) {\n      return;\n    }\n    const prevQuery = this.#currentQuery;\n    this.#currentQuery = query;\n    this.#currentQueryInitialState = query.state;\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this);\n      query.addObserver(this);\n    }\n  }\n  onQueryUpdate() {\n    this.updateResult();\n    if (this.hasListeners()) {\n      this.#updateTimers();\n    }\n  }\n  #notify(notifyOptions) {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(() => {\n      if (notifyOptions.listeners) {\n        this.listeners.forEach((listener) => {\n          listener(this.#currentResult);\n        });\n      }\n      this.#client.getQueryCache().notify({\n        query: this.#currentQuery,\n        type: \"observerResultsUpdated\"\n      });\n    });\n  }\n};\nfunction shouldLoadOnMount(query, options) {\n  return (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(options.enabled, query) !== false && query.state.data === void 0 && !(query.state.status === \"error\" && options.retryOnMount === false);\n}\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.data !== void 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\nfunction shouldFetchOn(query, options, field) {\n  if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(options.enabled, query) !== false && (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveStaleTime)(options.staleTime, query) !== \"static\") {\n    const value = typeof field === \"function\" ? field(query) : field;\n    return value === \"always\" || value !== false && isStale(query, options);\n  }\n  return false;\n}\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return (query !== prevQuery || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(prevOptions.enabled, query) === false) && (!options.suspense || query.state.status !== \"error\") && isStale(query, options);\n}\nfunction isStale(query, options) {\n  return (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(options.enabled, query) !== false && query.isStaleByTime((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveStaleTime)(options.staleTime, query));\n}\nfunction shouldAssignObserverCurrentProperties(observer, optimisticResult) {\n  if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.shallowEqualObjects)(observer.getCurrentResult(), optimisticResult)) {\n    return true;\n  }\n  return false;\n}\n\n//# sourceMappingURL=queryObserver.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/removable.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Removable: () => (/* binding */ Removable)\n/* harmony export */ });\n/* harmony import */ var _timeoutManager_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./timeoutManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/timeoutManager.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/removable.ts\n\n\nvar Removable = class {\n  #gcTimeout;\n  destroy() {\n    this.clearGcTimeout();\n  }\n  scheduleGc() {\n    this.clearGcTimeout();\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isValidTimeout)(this.gcTime)) {\n      this.#gcTimeout = _timeoutManager_js__WEBPACK_IMPORTED_MODULE_1__.timeoutManager.setTimeout(() => {\n        this.optionalRemove();\n      }, this.gcTime);\n    }\n  }\n  updateGcTime(newGcTime) {\n    this.gcTime = Math.max(\n      this.gcTime || 0,\n      newGcTime ?? (_utils_js__WEBPACK_IMPORTED_MODULE_0__.isServer ? Infinity : 5 * 60 * 1e3)\n    );\n  }\n  clearGcTimeout() {\n    if (this.#gcTimeout) {\n      _timeoutManager_js__WEBPACK_IMPORTED_MODULE_1__.timeoutManager.clearTimeout(this.#gcTimeout);\n      this.#gcTimeout = void 0;\n    }\n  }\n};\n\n//# sourceMappingURL=removable.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3JlbW92YWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNxRDtBQUNDO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSx5REFBYztBQUN0Qix3QkFBd0IsOERBQWM7QUFDdEM7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQiwrQ0FBUTtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sOERBQWM7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEVZT1NJXFxEcm9wYm94XFxQQ1xcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxwZXJzb25hbC1qb3VybmFsLWJsb2dcXG5vZGVfbW9kdWxlc1xcQHRhbnN0YWNrXFxxdWVyeS1jb3JlXFxidWlsZFxcbW9kZXJuXFxyZW1vdmFibGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3JlbW92YWJsZS50c1xuaW1wb3J0IHsgdGltZW91dE1hbmFnZXIgfSBmcm9tIFwiLi90aW1lb3V0TWFuYWdlci5qc1wiO1xuaW1wb3J0IHsgaXNTZXJ2ZXIsIGlzVmFsaWRUaW1lb3V0IH0gZnJvbSBcIi4vdXRpbHMuanNcIjtcbnZhciBSZW1vdmFibGUgPSBjbGFzcyB7XG4gICNnY1RpbWVvdXQ7XG4gIGRlc3Ryb3koKSB7XG4gICAgdGhpcy5jbGVhckdjVGltZW91dCgpO1xuICB9XG4gIHNjaGVkdWxlR2MoKSB7XG4gICAgdGhpcy5jbGVhckdjVGltZW91dCgpO1xuICAgIGlmIChpc1ZhbGlkVGltZW91dCh0aGlzLmdjVGltZSkpIHtcbiAgICAgIHRoaXMuI2djVGltZW91dCA9IHRpbWVvdXRNYW5hZ2VyLnNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICB0aGlzLm9wdGlvbmFsUmVtb3ZlKCk7XG4gICAgICB9LCB0aGlzLmdjVGltZSk7XG4gICAgfVxuICB9XG4gIHVwZGF0ZUdjVGltZShuZXdHY1RpbWUpIHtcbiAgICB0aGlzLmdjVGltZSA9IE1hdGgubWF4KFxuICAgICAgdGhpcy5nY1RpbWUgfHwgMCxcbiAgICAgIG5ld0djVGltZSA/PyAoaXNTZXJ2ZXIgPyBJbmZpbml0eSA6IDUgKiA2MCAqIDFlMylcbiAgICApO1xuICB9XG4gIGNsZWFyR2NUaW1lb3V0KCkge1xuICAgIGlmICh0aGlzLiNnY1RpbWVvdXQpIHtcbiAgICAgIHRpbWVvdXRNYW5hZ2VyLmNsZWFyVGltZW91dCh0aGlzLiNnY1RpbWVvdXQpO1xuICAgICAgdGhpcy4jZ2NUaW1lb3V0ID0gdm9pZCAwO1xuICAgIH1cbiAgfVxufTtcbmV4cG9ydCB7XG4gIFJlbW92YWJsZVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlbW92YWJsZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/retryer.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CancelledError: () => (/* binding */ CancelledError),\n/* harmony export */   canFetch: () => (/* binding */ canFetch),\n/* harmony export */   createRetryer: () => (/* binding */ createRetryer),\n/* harmony export */   isCancelledError: () => (/* binding */ isCancelledError)\n/* harmony export */ });\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./onlineManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _thenable_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./thenable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/retryer.ts\n\n\n\n\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1e3 * 2 ** failureCount, 3e4);\n}\nfunction canFetch(networkMode) {\n  return (networkMode ?? \"online\") === \"online\" ? _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline() : true;\n}\nvar CancelledError = class extends Error {\n  constructor(options) {\n    super(\"CancelledError\");\n    this.revert = options?.revert;\n    this.silent = options?.silent;\n  }\n};\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n  let isRetryCancelled = false;\n  let failureCount = 0;\n  let continueFn;\n  const thenable = (0,_thenable_js__WEBPACK_IMPORTED_MODULE_1__.pendingThenable)();\n  const isResolved = () => thenable.status !== \"pending\";\n  const cancel = (cancelOptions) => {\n    if (!isResolved()) {\n      const error = new CancelledError(cancelOptions);\n      reject(error);\n      config.onCancel?.(error);\n    }\n  };\n  const cancelRetry = () => {\n    isRetryCancelled = true;\n  };\n  const continueRetry = () => {\n    isRetryCancelled = false;\n  };\n  const canContinue = () => _focusManager_js__WEBPACK_IMPORTED_MODULE_2__.focusManager.isFocused() && (config.networkMode === \"always\" || _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline()) && config.canRun();\n  const canStart = () => canFetch(config.networkMode) && config.canRun();\n  const resolve = (value) => {\n    if (!isResolved()) {\n      continueFn?.();\n      thenable.resolve(value);\n    }\n  };\n  const reject = (value) => {\n    if (!isResolved()) {\n      continueFn?.();\n      thenable.reject(value);\n    }\n  };\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        if (isResolved() || canContinue()) {\n          continueResolve(value);\n        }\n      };\n      config.onPause?.();\n    }).then(() => {\n      continueFn = void 0;\n      if (!isResolved()) {\n        config.onContinue?.();\n      }\n    });\n  };\n  const run = () => {\n    if (isResolved()) {\n      return;\n    }\n    let promiseOrValue;\n    const initialPromise = failureCount === 0 ? config.initialPromise : void 0;\n    try {\n      promiseOrValue = initialPromise ?? config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    }\n    Promise.resolve(promiseOrValue).then(resolve).catch((error) => {\n      if (isResolved()) {\n        return;\n      }\n      const retry = config.retry ?? (_utils_js__WEBPACK_IMPORTED_MODULE_3__.isServer ? 0 : 3);\n      const retryDelay = config.retryDelay ?? defaultRetryDelay;\n      const delay = typeof retryDelay === \"function\" ? retryDelay(failureCount, error) : retryDelay;\n      const shouldRetry = retry === true || typeof retry === \"number\" && failureCount < retry || typeof retry === \"function\" && retry(failureCount, error);\n      if (isRetryCancelled || !shouldRetry) {\n        reject(error);\n        return;\n      }\n      failureCount++;\n      config.onFail?.(failureCount, error);\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.sleep)(delay).then(() => {\n        return canContinue() ? void 0 : pause();\n      }).then(() => {\n        if (isRetryCancelled) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  };\n  return {\n    promise: thenable,\n    status: () => thenable.status,\n    cancel,\n    continue: () => {\n      continueFn?.();\n      return thenable;\n    },\n    cancelRetry,\n    continueRetry,\n    canStart,\n    start: () => {\n      if (canStart()) {\n        run();\n      } else {\n        pause().then(run);\n      }\n      return thenable;\n    }\n  };\n}\n\n//# sourceMappingURL=retryer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/subscribable.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Subscribable: () => (/* binding */ Subscribable)\n/* harmony export */ });\n// src/subscribable.ts\nvar Subscribable = class {\n  constructor() {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n  subscribe(listener) {\n    this.listeners.add(listener);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(listener);\n      this.onUnsubscribe();\n    };\n  }\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n  onSubscribe() {\n  }\n  onUnsubscribe() {\n  }\n};\n\n//# sourceMappingURL=subscribable.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3N1YnNjcmliYWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEVZT1NJXFxEcm9wYm94XFxQQ1xcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxwZXJzb25hbC1qb3VybmFsLWJsb2dcXG5vZGVfbW9kdWxlc1xcQHRhbnN0YWNrXFxxdWVyeS1jb3JlXFxidWlsZFxcbW9kZXJuXFxzdWJzY3JpYmFibGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3N1YnNjcmliYWJsZS50c1xudmFyIFN1YnNjcmliYWJsZSA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy5saXN0ZW5lcnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFNldCgpO1xuICAgIHRoaXMuc3Vic2NyaWJlID0gdGhpcy5zdWJzY3JpYmUuYmluZCh0aGlzKTtcbiAgfVxuICBzdWJzY3JpYmUobGlzdGVuZXIpIHtcbiAgICB0aGlzLmxpc3RlbmVycy5hZGQobGlzdGVuZXIpO1xuICAgIHRoaXMub25TdWJzY3JpYmUoKTtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgdGhpcy5saXN0ZW5lcnMuZGVsZXRlKGxpc3RlbmVyKTtcbiAgICAgIHRoaXMub25VbnN1YnNjcmliZSgpO1xuICAgIH07XG4gIH1cbiAgaGFzTGlzdGVuZXJzKCkge1xuICAgIHJldHVybiB0aGlzLmxpc3RlbmVycy5zaXplID4gMDtcbiAgfVxuICBvblN1YnNjcmliZSgpIHtcbiAgfVxuICBvblVuc3Vic2NyaWJlKCkge1xuICB9XG59O1xuZXhwb3J0IHtcbiAgU3Vic2NyaWJhYmxlXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3Vic2NyaWJhYmxlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js":
/*!********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/thenable.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pendingThenable: () => (/* binding */ pendingThenable),\n/* harmony export */   tryResolveSync: () => (/* binding */ tryResolveSync)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/thenable.ts\n\nfunction pendingThenable() {\n  let resolve;\n  let reject;\n  const thenable = new Promise((_resolve, _reject) => {\n    resolve = _resolve;\n    reject = _reject;\n  });\n  thenable.status = \"pending\";\n  thenable.catch(() => {\n  });\n  function finalize(data) {\n    Object.assign(thenable, data);\n    delete thenable.resolve;\n    delete thenable.reject;\n  }\n  thenable.resolve = (value) => {\n    finalize({\n      status: \"fulfilled\",\n      value\n    });\n    resolve(value);\n  };\n  thenable.reject = (reason) => {\n    finalize({\n      status: \"rejected\",\n      reason\n    });\n    reject(reason);\n  };\n  return thenable;\n}\nfunction tryResolveSync(promise) {\n  let data;\n  promise.then((result) => {\n    data = result;\n    return result;\n  }, _utils_js__WEBPACK_IMPORTED_MODULE_0__.noop)?.catch(_utils_js__WEBPACK_IMPORTED_MODULE_0__.noop);\n  if (data !== void 0) {\n    return { data };\n  }\n  return void 0;\n}\n\n//# sourceMappingURL=thenable.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/timeoutManager.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/timeoutManager.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TimeoutManager: () => (/* binding */ TimeoutManager),\n/* harmony export */   defaultTimeoutProvider: () => (/* binding */ defaultTimeoutProvider),\n/* harmony export */   systemSetTimeoutZero: () => (/* binding */ systemSetTimeoutZero),\n/* harmony export */   timeoutManager: () => (/* binding */ timeoutManager)\n/* harmony export */ });\n// src/timeoutManager.ts\nvar defaultTimeoutProvider = {\n  // We need the wrapper function syntax below instead of direct references to\n  // global setTimeout etc.\n  //\n  // BAD: `setTimeout: setTimeout`\n  // GOOD: `setTimeout: (cb, delay) => setTimeout(cb, delay)`\n  //\n  // If we use direct references here, then anything that wants to spy on or\n  // replace the global setTimeout (like tests) won't work since we'll already\n  // have a hard reference to the original implementation at the time when this\n  // file was imported.\n  setTimeout: (callback, delay) => setTimeout(callback, delay),\n  clearTimeout: (timeoutId) => clearTimeout(timeoutId),\n  setInterval: (callback, delay) => setInterval(callback, delay),\n  clearInterval: (intervalId) => clearInterval(intervalId)\n};\nvar TimeoutManager = class {\n  // We cannot have TimeoutManager<T> as we must instantiate it with a concrete\n  // type at app boot; and if we leave that type, then any new timer provider\n  // would need to support ReturnType<typeof setTimeout>, which is infeasible.\n  //\n  // We settle for type safety for the TimeoutProvider type, and accept that\n  // this class is unsafe internally to allow for extension.\n  #provider = defaultTimeoutProvider;\n  #providerCalled = false;\n  setTimeoutProvider(provider) {\n    if (true) {\n      if (this.#providerCalled && provider !== this.#provider) {\n        console.error(\n          `[timeoutManager]: Switching provider after calls to previous provider might result in unexpected behavior.`,\n          { previous: this.#provider, provider }\n        );\n      }\n    }\n    this.#provider = provider;\n    if (true) {\n      this.#providerCalled = false;\n    }\n  }\n  setTimeout(callback, delay) {\n    if (true) {\n      this.#providerCalled = true;\n    }\n    return this.#provider.setTimeout(callback, delay);\n  }\n  clearTimeout(timeoutId) {\n    this.#provider.clearTimeout(timeoutId);\n  }\n  setInterval(callback, delay) {\n    if (true) {\n      this.#providerCalled = true;\n    }\n    return this.#provider.setInterval(callback, delay);\n  }\n  clearInterval(intervalId) {\n    this.#provider.clearInterval(intervalId);\n  }\n};\nvar timeoutManager = new TimeoutManager();\nfunction systemSetTimeoutZero(callback) {\n  setTimeout(callback, 0);\n}\n\n//# sourceMappingURL=timeoutManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/timeoutManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/utils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToEnd: () => (/* binding */ addToEnd),\n/* harmony export */   addToStart: () => (/* binding */ addToStart),\n/* harmony export */   ensureQueryFn: () => (/* binding */ ensureQueryFn),\n/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),\n/* harmony export */   hashKey: () => (/* binding */ hashKey),\n/* harmony export */   hashQueryKeyByOptions: () => (/* binding */ hashQueryKeyByOptions),\n/* harmony export */   isPlainArray: () => (/* binding */ isPlainArray),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isServer: () => (/* binding */ isServer),\n/* harmony export */   isValidTimeout: () => (/* binding */ isValidTimeout),\n/* harmony export */   keepPreviousData: () => (/* binding */ keepPreviousData),\n/* harmony export */   matchMutation: () => (/* binding */ matchMutation),\n/* harmony export */   matchQuery: () => (/* binding */ matchQuery),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   partialMatchKey: () => (/* binding */ partialMatchKey),\n/* harmony export */   replaceData: () => (/* binding */ replaceData),\n/* harmony export */   replaceEqualDeep: () => (/* binding */ replaceEqualDeep),\n/* harmony export */   resolveEnabled: () => (/* binding */ resolveEnabled),\n/* harmony export */   resolveStaleTime: () => (/* binding */ resolveStaleTime),\n/* harmony export */   shallowEqualObjects: () => (/* binding */ shallowEqualObjects),\n/* harmony export */   shouldThrowError: () => (/* binding */ shouldThrowError),\n/* harmony export */   skipToken: () => (/* binding */ skipToken),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   timeUntilStale: () => (/* binding */ timeUntilStale)\n/* harmony export */ });\n/* harmony import */ var _timeoutManager_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./timeoutManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/timeoutManager.js\");\n// src/utils.ts\n\nvar isServer = typeof window === \"undefined\" || \"Deno\" in globalThis;\nfunction noop() {\n}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === \"function\" ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === \"number\" && value >= 0 && value !== Infinity;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction resolveStaleTime(staleTime, query) {\n  return typeof staleTime === \"function\" ? staleTime(query) : staleTime;\n}\nfunction resolveEnabled(enabled, query) {\n  return typeof enabled === \"function\" ? enabled(query) : enabled;\n}\nfunction matchQuery(filters, query) {\n  const {\n    type = \"all\",\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale\n  } = filters;\n  if (queryKey) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n  if (type !== \"all\") {\n    const isActive = query.isActive();\n    if (type === \"active\" && !isActive) {\n      return false;\n    }\n    if (type === \"inactive\" && isActive) {\n      return false;\n    }\n  }\n  if (typeof stale === \"boolean\" && query.isStale() !== stale) {\n    return false;\n  }\n  if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n    return false;\n  }\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  const { exact, status, predicate, mutationKey } = filters;\n  if (mutationKey) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n    if (exact) {\n      if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n  if (status && mutation.state.status !== status) {\n    return false;\n  }\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  const hashFn = options?.queryKeyHashFn || hashKey;\n  return hashFn(queryKey);\n}\nfunction hashKey(queryKey) {\n  return JSON.stringify(\n    queryKey,\n    (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n      result[key] = val[key];\n      return result;\n    }, {}) : val\n  );\n}\nfunction partialMatchKey(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n    return Object.keys(b).every((key) => partialMatchKey(a[key], b[key]));\n  }\n  return false;\n}\nvar hasOwn = Object.prototype.hasOwnProperty;\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n  const array = isPlainArray(a) && isPlainArray(b);\n  if (!array && !(isPlainObject(a) && isPlainObject(b))) return b;\n  const aItems = array ? a : Object.keys(a);\n  const aSize = aItems.length;\n  const bItems = array ? b : Object.keys(b);\n  const bSize = bItems.length;\n  const copy = array ? new Array(bSize) : {};\n  let equalItems = 0;\n  for (let i = 0; i < bSize; i++) {\n    const key = array ? i : bItems[i];\n    const aItem = a[key];\n    const bItem = b[key];\n    if (aItem === bItem) {\n      copy[key] = aItem;\n      if (array ? i < aSize : hasOwn.call(a, key)) equalItems++;\n      continue;\n    }\n    if (aItem === null || bItem === null || typeof aItem !== \"object\" || typeof bItem !== \"object\") {\n      copy[key] = bItem;\n      continue;\n    }\n    const v = replaceEqualDeep(aItem, bItem);\n    copy[key] = v;\n    if (v === aItem) equalItems++;\n  }\n  return aSize === bSize && equalItems === aSize ? a : copy;\n}\nfunction shallowEqualObjects(a, b) {\n  if (!b || Object.keys(a).length !== Object.keys(b).length) {\n    return false;\n  }\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isPlainArray(value) {\n  return Array.isArray(value) && value.length === Object.keys(value).length;\n}\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  }\n  const ctor = o.constructor;\n  if (ctor === void 0) {\n    return true;\n  }\n  const prot = ctor.prototype;\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  }\n  if (!prot.hasOwnProperty(\"isPrototypeOf\")) {\n    return false;\n  }\n  if (Object.getPrototypeOf(o) !== Object.prototype) {\n    return false;\n  }\n  return true;\n}\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === \"[object Object]\";\n}\nfunction sleep(timeout) {\n  return new Promise((resolve) => {\n    _timeoutManager_js__WEBPACK_IMPORTED_MODULE_0__.timeoutManager.setTimeout(resolve, timeout);\n  });\n}\nfunction replaceData(prevData, data, options) {\n  if (typeof options.structuralSharing === \"function\") {\n    return options.structuralSharing(prevData, data);\n  } else if (options.structuralSharing !== false) {\n    if (true) {\n      try {\n        return replaceEqualDeep(prevData, data);\n      } catch (error) {\n        console.error(\n          `Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`\n        );\n        throw error;\n      }\n    }\n    return replaceEqualDeep(prevData, data);\n  }\n  return data;\n}\nfunction keepPreviousData(previousData) {\n  return previousData;\n}\nfunction addToEnd(items, item, max = 0) {\n  const newItems = [...items, item];\n  return max && newItems.length > max ? newItems.slice(1) : newItems;\n}\nfunction addToStart(items, item, max = 0) {\n  const newItems = [item, ...items];\n  return max && newItems.length > max ? newItems.slice(0, -1) : newItems;\n}\nvar skipToken = Symbol();\nfunction ensureQueryFn(options, fetchOptions) {\n  if (true) {\n    if (options.queryFn === skipToken) {\n      console.error(\n        `Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`\n      );\n    }\n  }\n  if (!options.queryFn && fetchOptions?.initialPromise) {\n    return () => fetchOptions.initialPromise;\n  }\n  if (!options.queryFn || options.queryFn === skipToken) {\n    return () => Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`));\n  }\n  return options.queryFn;\n}\nfunction shouldThrowError(throwOnError, params) {\n  if (typeof throwOnError === \"function\") {\n    return throwOnError(...params);\n  }\n  return !!throwOnError;\n}\n\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3V0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNxRDtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSx3Q0FBd0M7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLLElBQUk7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsV0FBVztBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLDhEQUFjO0FBQ2xCLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSixRQUFRLElBQXFDO0FBQzdDO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQSxvS0FBb0ssa0JBQWtCLEtBQUssTUFBTTtBQUNqTTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLElBQXFDO0FBQzNDO0FBQ0E7QUFDQSxpSEFBaUgsa0JBQWtCO0FBQ25JO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0RBQStELGtCQUFrQjtBQUNqRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUEwQkU7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxFWU9TSVxcRHJvcGJveFxcUENcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xccGVyc29uYWwtam91cm5hbC1ibG9nXFxub2RlX21vZHVsZXNcXEB0YW5zdGFja1xccXVlcnktY29yZVxcYnVpbGRcXG1vZGVyblxcdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3V0aWxzLnRzXG5pbXBvcnQgeyB0aW1lb3V0TWFuYWdlciB9IGZyb20gXCIuL3RpbWVvdXRNYW5hZ2VyLmpzXCI7XG52YXIgaXNTZXJ2ZXIgPSB0eXBlb2Ygd2luZG93ID09PSBcInVuZGVmaW5lZFwiIHx8IFwiRGVub1wiIGluIGdsb2JhbFRoaXM7XG5mdW5jdGlvbiBub29wKCkge1xufVxuZnVuY3Rpb24gZnVuY3Rpb25hbFVwZGF0ZSh1cGRhdGVyLCBpbnB1dCkge1xuICByZXR1cm4gdHlwZW9mIHVwZGF0ZXIgPT09IFwiZnVuY3Rpb25cIiA/IHVwZGF0ZXIoaW5wdXQpIDogdXBkYXRlcjtcbn1cbmZ1bmN0aW9uIGlzVmFsaWRUaW1lb3V0KHZhbHVlKSB7XG4gIHJldHVybiB0eXBlb2YgdmFsdWUgPT09IFwibnVtYmVyXCIgJiYgdmFsdWUgPj0gMCAmJiB2YWx1ZSAhPT0gSW5maW5pdHk7XG59XG5mdW5jdGlvbiB0aW1lVW50aWxTdGFsZSh1cGRhdGVkQXQsIHN0YWxlVGltZSkge1xuICByZXR1cm4gTWF0aC5tYXgodXBkYXRlZEF0ICsgKHN0YWxlVGltZSB8fCAwKSAtIERhdGUubm93KCksIDApO1xufVxuZnVuY3Rpb24gcmVzb2x2ZVN0YWxlVGltZShzdGFsZVRpbWUsIHF1ZXJ5KSB7XG4gIHJldHVybiB0eXBlb2Ygc3RhbGVUaW1lID09PSBcImZ1bmN0aW9uXCIgPyBzdGFsZVRpbWUocXVlcnkpIDogc3RhbGVUaW1lO1xufVxuZnVuY3Rpb24gcmVzb2x2ZUVuYWJsZWQoZW5hYmxlZCwgcXVlcnkpIHtcbiAgcmV0dXJuIHR5cGVvZiBlbmFibGVkID09PSBcImZ1bmN0aW9uXCIgPyBlbmFibGVkKHF1ZXJ5KSA6IGVuYWJsZWQ7XG59XG5mdW5jdGlvbiBtYXRjaFF1ZXJ5KGZpbHRlcnMsIHF1ZXJ5KSB7XG4gIGNvbnN0IHtcbiAgICB0eXBlID0gXCJhbGxcIixcbiAgICBleGFjdCxcbiAgICBmZXRjaFN0YXR1cyxcbiAgICBwcmVkaWNhdGUsXG4gICAgcXVlcnlLZXksXG4gICAgc3RhbGVcbiAgfSA9IGZpbHRlcnM7XG4gIGlmIChxdWVyeUtleSkge1xuICAgIGlmIChleGFjdCkge1xuICAgICAgaWYgKHF1ZXJ5LnF1ZXJ5SGFzaCAhPT0gaGFzaFF1ZXJ5S2V5QnlPcHRpb25zKHF1ZXJ5S2V5LCBxdWVyeS5vcHRpb25zKSkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG4gICAgfSBlbHNlIGlmICghcGFydGlhbE1hdGNoS2V5KHF1ZXJ5LnF1ZXJ5S2V5LCBxdWVyeUtleSkpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cbiAgaWYgKHR5cGUgIT09IFwiYWxsXCIpIHtcbiAgICBjb25zdCBpc0FjdGl2ZSA9IHF1ZXJ5LmlzQWN0aXZlKCk7XG4gICAgaWYgKHR5cGUgPT09IFwiYWN0aXZlXCIgJiYgIWlzQWN0aXZlKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGlmICh0eXBlID09PSBcImluYWN0aXZlXCIgJiYgaXNBY3RpdmUpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cbiAgaWYgKHR5cGVvZiBzdGFsZSA9PT0gXCJib29sZWFuXCIgJiYgcXVlcnkuaXNTdGFsZSgpICE9PSBzdGFsZSkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICBpZiAoZmV0Y2hTdGF0dXMgJiYgZmV0Y2hTdGF0dXMgIT09IHF1ZXJ5LnN0YXRlLmZldGNoU3RhdHVzKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIGlmIChwcmVkaWNhdGUgJiYgIXByZWRpY2F0ZShxdWVyeSkpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbiAgcmV0dXJuIHRydWU7XG59XG5mdW5jdGlvbiBtYXRjaE11dGF0aW9uKGZpbHRlcnMsIG11dGF0aW9uKSB7XG4gIGNvbnN0IHsgZXhhY3QsIHN0YXR1cywgcHJlZGljYXRlLCBtdXRhdGlvbktleSB9ID0gZmlsdGVycztcbiAgaWYgKG11dGF0aW9uS2V5KSB7XG4gICAgaWYgKCFtdXRhdGlvbi5vcHRpb25zLm11dGF0aW9uS2V5KSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGlmIChleGFjdCkge1xuICAgICAgaWYgKGhhc2hLZXkobXV0YXRpb24ub3B0aW9ucy5tdXRhdGlvbktleSkgIT09IGhhc2hLZXkobXV0YXRpb25LZXkpKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cbiAgICB9IGVsc2UgaWYgKCFwYXJ0aWFsTWF0Y2hLZXkobXV0YXRpb24ub3B0aW9ucy5tdXRhdGlvbktleSwgbXV0YXRpb25LZXkpKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9XG4gIGlmIChzdGF0dXMgJiYgbXV0YXRpb24uc3RhdGUuc3RhdHVzICE9PSBzdGF0dXMpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbiAgaWYgKHByZWRpY2F0ZSAmJiAhcHJlZGljYXRlKG11dGF0aW9uKSkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICByZXR1cm4gdHJ1ZTtcbn1cbmZ1bmN0aW9uIGhhc2hRdWVyeUtleUJ5T3B0aW9ucyhxdWVyeUtleSwgb3B0aW9ucykge1xuICBjb25zdCBoYXNoRm4gPSBvcHRpb25zPy5xdWVyeUtleUhhc2hGbiB8fCBoYXNoS2V5O1xuICByZXR1cm4gaGFzaEZuKHF1ZXJ5S2V5KTtcbn1cbmZ1bmN0aW9uIGhhc2hLZXkocXVlcnlLZXkpIHtcbiAgcmV0dXJuIEpTT04uc3RyaW5naWZ5KFxuICAgIHF1ZXJ5S2V5LFxuICAgIChfLCB2YWwpID0+IGlzUGxhaW5PYmplY3QodmFsKSA/IE9iamVjdC5rZXlzKHZhbCkuc29ydCgpLnJlZHVjZSgocmVzdWx0LCBrZXkpID0+IHtcbiAgICAgIHJlc3VsdFtrZXldID0gdmFsW2tleV07XG4gICAgICByZXR1cm4gcmVzdWx0O1xuICAgIH0sIHt9KSA6IHZhbFxuICApO1xufVxuZnVuY3Rpb24gcGFydGlhbE1hdGNoS2V5KGEsIGIpIHtcbiAgaWYgKGEgPT09IGIpIHtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfVxuICBpZiAodHlwZW9mIGEgIT09IHR5cGVvZiBiKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIGlmIChhICYmIGIgJiYgdHlwZW9mIGEgPT09IFwib2JqZWN0XCIgJiYgdHlwZW9mIGIgPT09IFwib2JqZWN0XCIpIHtcbiAgICByZXR1cm4gT2JqZWN0LmtleXMoYikuZXZlcnkoKGtleSkgPT4gcGFydGlhbE1hdGNoS2V5KGFba2V5XSwgYltrZXldKSk7XG4gIH1cbiAgcmV0dXJuIGZhbHNlO1xufVxudmFyIGhhc093biA9IE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHk7XG5mdW5jdGlvbiByZXBsYWNlRXF1YWxEZWVwKGEsIGIpIHtcbiAgaWYgKGEgPT09IGIpIHtcbiAgICByZXR1cm4gYTtcbiAgfVxuICBjb25zdCBhcnJheSA9IGlzUGxhaW5BcnJheShhKSAmJiBpc1BsYWluQXJyYXkoYik7XG4gIGlmICghYXJyYXkgJiYgIShpc1BsYWluT2JqZWN0KGEpICYmIGlzUGxhaW5PYmplY3QoYikpKSByZXR1cm4gYjtcbiAgY29uc3QgYUl0ZW1zID0gYXJyYXkgPyBhIDogT2JqZWN0LmtleXMoYSk7XG4gIGNvbnN0IGFTaXplID0gYUl0ZW1zLmxlbmd0aDtcbiAgY29uc3QgYkl0ZW1zID0gYXJyYXkgPyBiIDogT2JqZWN0LmtleXMoYik7XG4gIGNvbnN0IGJTaXplID0gYkl0ZW1zLmxlbmd0aDtcbiAgY29uc3QgY29weSA9IGFycmF5ID8gbmV3IEFycmF5KGJTaXplKSA6IHt9O1xuICBsZXQgZXF1YWxJdGVtcyA9IDA7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgYlNpemU7IGkrKykge1xuICAgIGNvbnN0IGtleSA9IGFycmF5ID8gaSA6IGJJdGVtc1tpXTtcbiAgICBjb25zdCBhSXRlbSA9IGFba2V5XTtcbiAgICBjb25zdCBiSXRlbSA9IGJba2V5XTtcbiAgICBpZiAoYUl0ZW0gPT09IGJJdGVtKSB7XG4gICAgICBjb3B5W2tleV0gPSBhSXRlbTtcbiAgICAgIGlmIChhcnJheSA/IGkgPCBhU2l6ZSA6IGhhc093bi5jYWxsKGEsIGtleSkpIGVxdWFsSXRlbXMrKztcbiAgICAgIGNvbnRpbnVlO1xuICAgIH1cbiAgICBpZiAoYUl0ZW0gPT09IG51bGwgfHwgYkl0ZW0gPT09IG51bGwgfHwgdHlwZW9mIGFJdGVtICE9PSBcIm9iamVjdFwiIHx8IHR5cGVvZiBiSXRlbSAhPT0gXCJvYmplY3RcIikge1xuICAgICAgY29weVtrZXldID0gYkl0ZW07XG4gICAgICBjb250aW51ZTtcbiAgICB9XG4gICAgY29uc3QgdiA9IHJlcGxhY2VFcXVhbERlZXAoYUl0ZW0sIGJJdGVtKTtcbiAgICBjb3B5W2tleV0gPSB2O1xuICAgIGlmICh2ID09PSBhSXRlbSkgZXF1YWxJdGVtcysrO1xuICB9XG4gIHJldHVybiBhU2l6ZSA9PT0gYlNpemUgJiYgZXF1YWxJdGVtcyA9PT0gYVNpemUgPyBhIDogY29weTtcbn1cbmZ1bmN0aW9uIHNoYWxsb3dFcXVhbE9iamVjdHMoYSwgYikge1xuICBpZiAoIWIgfHwgT2JqZWN0LmtleXMoYSkubGVuZ3RoICE9PSBPYmplY3Qua2V5cyhiKS5sZW5ndGgpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbiAgZm9yIChjb25zdCBrZXkgaW4gYSkge1xuICAgIGlmIChhW2tleV0gIT09IGJba2V5XSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfVxuICByZXR1cm4gdHJ1ZTtcbn1cbmZ1bmN0aW9uIGlzUGxhaW5BcnJheSh2YWx1ZSkge1xuICByZXR1cm4gQXJyYXkuaXNBcnJheSh2YWx1ZSkgJiYgdmFsdWUubGVuZ3RoID09PSBPYmplY3Qua2V5cyh2YWx1ZSkubGVuZ3RoO1xufVxuZnVuY3Rpb24gaXNQbGFpbk9iamVjdChvKSB7XG4gIGlmICghaGFzT2JqZWN0UHJvdG90eXBlKG8pKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIGNvbnN0IGN0b3IgPSBvLmNvbnN0cnVjdG9yO1xuICBpZiAoY3RvciA9PT0gdm9pZCAwKSB7XG4gICAgcmV0dXJuIHRydWU7XG4gIH1cbiAgY29uc3QgcHJvdCA9IGN0b3IucHJvdG90eXBlO1xuICBpZiAoIWhhc09iamVjdFByb3RvdHlwZShwcm90KSkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICBpZiAoIXByb3QuaGFzT3duUHJvcGVydHkoXCJpc1Byb3RvdHlwZU9mXCIpKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIGlmIChPYmplY3QuZ2V0UHJvdG90eXBlT2YobykgIT09IE9iamVjdC5wcm90b3R5cGUpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbiAgcmV0dXJuIHRydWU7XG59XG5mdW5jdGlvbiBoYXNPYmplY3RQcm90b3R5cGUobykge1xuICByZXR1cm4gT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKG8pID09PSBcIltvYmplY3QgT2JqZWN0XVwiO1xufVxuZnVuY3Rpb24gc2xlZXAodGltZW91dCkge1xuICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHtcbiAgICB0aW1lb3V0TWFuYWdlci5zZXRUaW1lb3V0KHJlc29sdmUsIHRpbWVvdXQpO1xuICB9KTtcbn1cbmZ1bmN0aW9uIHJlcGxhY2VEYXRhKHByZXZEYXRhLCBkYXRhLCBvcHRpb25zKSB7XG4gIGlmICh0eXBlb2Ygb3B0aW9ucy5zdHJ1Y3R1cmFsU2hhcmluZyA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgcmV0dXJuIG9wdGlvbnMuc3RydWN0dXJhbFNoYXJpbmcocHJldkRhdGEsIGRhdGEpO1xuICB9IGVsc2UgaWYgKG9wdGlvbnMuc3RydWN0dXJhbFNoYXJpbmcgIT09IGZhbHNlKSB7XG4gICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIikge1xuICAgICAgdHJ5IHtcbiAgICAgICAgcmV0dXJuIHJlcGxhY2VFcXVhbERlZXAocHJldkRhdGEsIGRhdGEpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICBgU3RydWN0dXJhbCBzaGFyaW5nIHJlcXVpcmVzIGRhdGEgdG8gYmUgSlNPTiBzZXJpYWxpemFibGUuIFRvIGZpeCB0aGlzLCB0dXJuIG9mZiBzdHJ1Y3R1cmFsU2hhcmluZyBvciByZXR1cm4gSlNPTi1zZXJpYWxpemFibGUgZGF0YSBmcm9tIHlvdXIgcXVlcnlGbi4gWyR7b3B0aW9ucy5xdWVyeUhhc2h9XTogJHtlcnJvcn1gXG4gICAgICAgICk7XG4gICAgICAgIHRocm93IGVycm9yO1xuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gcmVwbGFjZUVxdWFsRGVlcChwcmV2RGF0YSwgZGF0YSk7XG4gIH1cbiAgcmV0dXJuIGRhdGE7XG59XG5mdW5jdGlvbiBrZWVwUHJldmlvdXNEYXRhKHByZXZpb3VzRGF0YSkge1xuICByZXR1cm4gcHJldmlvdXNEYXRhO1xufVxuZnVuY3Rpb24gYWRkVG9FbmQoaXRlbXMsIGl0ZW0sIG1heCA9IDApIHtcbiAgY29uc3QgbmV3SXRlbXMgPSBbLi4uaXRlbXMsIGl0ZW1dO1xuICByZXR1cm4gbWF4ICYmIG5ld0l0ZW1zLmxlbmd0aCA+IG1heCA/IG5ld0l0ZW1zLnNsaWNlKDEpIDogbmV3SXRlbXM7XG59XG5mdW5jdGlvbiBhZGRUb1N0YXJ0KGl0ZW1zLCBpdGVtLCBtYXggPSAwKSB7XG4gIGNvbnN0IG5ld0l0ZW1zID0gW2l0ZW0sIC4uLml0ZW1zXTtcbiAgcmV0dXJuIG1heCAmJiBuZXdJdGVtcy5sZW5ndGggPiBtYXggPyBuZXdJdGVtcy5zbGljZSgwLCAtMSkgOiBuZXdJdGVtcztcbn1cbnZhciBza2lwVG9rZW4gPSBTeW1ib2woKTtcbmZ1bmN0aW9uIGVuc3VyZVF1ZXJ5Rm4ob3B0aW9ucywgZmV0Y2hPcHRpb25zKSB7XG4gIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIHtcbiAgICBpZiAob3B0aW9ucy5xdWVyeUZuID09PSBza2lwVG9rZW4pIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgIGBBdHRlbXB0ZWQgdG8gaW52b2tlIHF1ZXJ5Rm4gd2hlbiBzZXQgdG8gc2tpcFRva2VuLiBUaGlzIGlzIGxpa2VseSBhIGNvbmZpZ3VyYXRpb24gZXJyb3IuIFF1ZXJ5IGhhc2g6ICcke29wdGlvbnMucXVlcnlIYXNofSdgXG4gICAgICApO1xuICAgIH1cbiAgfVxuICBpZiAoIW9wdGlvbnMucXVlcnlGbiAmJiBmZXRjaE9wdGlvbnM/LmluaXRpYWxQcm9taXNlKSB7XG4gICAgcmV0dXJuICgpID0+IGZldGNoT3B0aW9ucy5pbml0aWFsUHJvbWlzZTtcbiAgfVxuICBpZiAoIW9wdGlvbnMucXVlcnlGbiB8fCBvcHRpb25zLnF1ZXJ5Rm4gPT09IHNraXBUb2tlbikge1xuICAgIHJldHVybiAoKSA9PiBQcm9taXNlLnJlamVjdChuZXcgRXJyb3IoYE1pc3NpbmcgcXVlcnlGbjogJyR7b3B0aW9ucy5xdWVyeUhhc2h9J2ApKTtcbiAgfVxuICByZXR1cm4gb3B0aW9ucy5xdWVyeUZuO1xufVxuZnVuY3Rpb24gc2hvdWxkVGhyb3dFcnJvcih0aHJvd09uRXJyb3IsIHBhcmFtcykge1xuICBpZiAodHlwZW9mIHRocm93T25FcnJvciA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgcmV0dXJuIHRocm93T25FcnJvciguLi5wYXJhbXMpO1xuICB9XG4gIHJldHVybiAhIXRocm93T25FcnJvcjtcbn1cbmV4cG9ydCB7XG4gIGFkZFRvRW5kLFxuICBhZGRUb1N0YXJ0LFxuICBlbnN1cmVRdWVyeUZuLFxuICBmdW5jdGlvbmFsVXBkYXRlLFxuICBoYXNoS2V5LFxuICBoYXNoUXVlcnlLZXlCeU9wdGlvbnMsXG4gIGlzUGxhaW5BcnJheSxcbiAgaXNQbGFpbk9iamVjdCxcbiAgaXNTZXJ2ZXIsXG4gIGlzVmFsaWRUaW1lb3V0LFxuICBrZWVwUHJldmlvdXNEYXRhLFxuICBtYXRjaE11dGF0aW9uLFxuICBtYXRjaFF1ZXJ5LFxuICBub29wLFxuICBwYXJ0aWFsTWF0Y2hLZXksXG4gIHJlcGxhY2VEYXRhLFxuICByZXBsYWNlRXF1YWxEZWVwLFxuICByZXNvbHZlRW5hYmxlZCxcbiAgcmVzb2x2ZVN0YWxlVGltZSxcbiAgc2hhbGxvd0VxdWFsT2JqZWN0cyxcbiAgc2hvdWxkVGhyb3dFcnJvcixcbiAgc2tpcFRva2VuLFxuICBzbGVlcCxcbiAgdGltZVVudGlsU3RhbGVcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlscy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HydrationBoundary: () => (/* binding */ HydrationBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/hydration.js\");\n/* harmony import */ var _QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* __next_internal_client_entry_do_not_use__ HydrationBoundary auto */ // src/HydrationBoundary.tsx\n\n\n\nvar HydrationBoundary = ({ children, options = {}, state, queryClient })=>{\n    const client = (0,_QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)(queryClient);\n    const optionsRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(options);\n    optionsRef.current = options;\n    const hydrationQueue = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"HydrationBoundary.useMemo[hydrationQueue]\": ()=>{\n            if (state) {\n                if (typeof state !== \"object\") {\n                    return;\n                }\n                const queryCache = client.getQueryCache();\n                const queries = state.queries || [];\n                const newQueries = [];\n                const existingQueries = [];\n                for (const dehydratedQuery of queries){\n                    const existingQuery = queryCache.get(dehydratedQuery.queryHash);\n                    if (!existingQuery) {\n                        newQueries.push(dehydratedQuery);\n                    } else {\n                        const hydrationIsNewer = dehydratedQuery.state.dataUpdatedAt > existingQuery.state.dataUpdatedAt || dehydratedQuery.promise && existingQuery.state.status !== \"pending\" && existingQuery.state.fetchStatus !== \"fetching\" && dehydratedQuery.dehydratedAt !== void 0 && dehydratedQuery.dehydratedAt > existingQuery.state.dataUpdatedAt;\n                        if (hydrationIsNewer) {\n                            existingQueries.push(dehydratedQuery);\n                        }\n                    }\n                }\n                if (newQueries.length > 0) {\n                    (0,_tanstack_query_core__WEBPACK_IMPORTED_MODULE_2__.hydrate)(client, {\n                        queries: newQueries\n                    }, optionsRef.current);\n                }\n                if (existingQueries.length > 0) {\n                    return existingQueries;\n                }\n            }\n            return void 0;\n        }\n    }[\"HydrationBoundary.useMemo[hydrationQueue]\"], [\n        client,\n        state\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"HydrationBoundary.useEffect\": ()=>{\n            if (hydrationQueue) {\n                (0,_tanstack_query_core__WEBPACK_IMPORTED_MODULE_2__.hydrate)(client, {\n                    queries: hydrationQueue\n                }, optionsRef.current);\n            }\n        }\n    }[\"HydrationBoundary.useEffect\"], [\n        client,\n        hydrationQueue\n    ]);\n    return children;\n};\n //# sourceMappingURL=HydrationBoundary.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IsRestoringProvider: () => (/* binding */ IsRestoringProvider),\n/* harmony export */   useIsRestoring: () => (/* binding */ useIsRestoring)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ IsRestoringProvider,useIsRestoring auto */ // src/IsRestoringProvider.ts\n\nvar IsRestoringContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(false);\nvar useIsRestoring = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(IsRestoringContext);\nvar IsRestoringProvider = IsRestoringContext.Provider;\n //# sourceMappingURL=IsRestoringProvider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL21vZGVybi9Jc1Jlc3RvcmluZ1Byb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFDdUI7QUFFdkIsSUFBTSxtQ0FBMkIsaURBQWMsS0FBSztBQUU3QyxJQUFNLGlCQUFpQixJQUFZLDhDQUFXLGtCQUFrQjtBQUNoRSxJQUFNLHNCQUFzQixtQkFBbUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRVlPU0lcXERyb3Bib3hcXFBDXFxEb2N1bWVudHNcXHNyY1xcSXNSZXN0b3JpbmdQcm92aWRlci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0J1xuXG5jb25zdCBJc1Jlc3RvcmluZ0NvbnRleHQgPSBSZWFjdC5jcmVhdGVDb250ZXh0KGZhbHNlKVxuXG5leHBvcnQgY29uc3QgdXNlSXNSZXN0b3JpbmcgPSAoKSA9PiBSZWFjdC51c2VDb250ZXh0KElzUmVzdG9yaW5nQ29udGV4dClcbmV4cG9ydCBjb25zdCBJc1Jlc3RvcmluZ1Byb3ZpZGVyID0gSXNSZXN0b3JpbmdDb250ZXh0LlByb3ZpZGVyXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClientContext: () => (/* binding */ QueryClientContext),\n/* harmony export */   QueryClientProvider: () => (/* binding */ QueryClientProvider),\n/* harmony export */   useQueryClient: () => (/* binding */ useQueryClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryClientContext,QueryClientProvider,useQueryClient auto */ // src/QueryClientProvider.tsx\n\n\nvar QueryClientContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar useQueryClient = (queryClient)=>{\n    const client = react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryClientContext);\n    if (queryClient) {\n        return queryClient;\n    }\n    if (!client) {\n        throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n    }\n    return client;\n};\nvar QueryClientProvider = ({ client, children })=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"QueryClientProvider.useEffect\": ()=>{\n            client.mount();\n            return ({\n                \"QueryClientProvider.useEffect\": ()=>{\n                    client.unmount();\n                }\n            })[\"QueryClientProvider.useEffect\"];\n        }\n    }[\"QueryClientProvider.useEffect\"], [\n        client\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryClientContext.Provider, {\n        value: client,\n        children\n    });\n};\n //# sourceMappingURL=QueryClientProvider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryErrorResetBoundary: () => (/* binding */ QueryErrorResetBoundary),\n/* harmony export */   useQueryErrorResetBoundary: () => (/* binding */ useQueryErrorResetBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryErrorResetBoundary,useQueryErrorResetBoundary auto */ // src/QueryErrorResetBoundary.tsx\n\n\nfunction createValue() {\n    let isReset = false;\n    return {\n        clearReset: ()=>{\n            isReset = false;\n        },\n        reset: ()=>{\n            isReset = true;\n        },\n        isReset: ()=>{\n            return isReset;\n        }\n    };\n}\nvar QueryErrorResetBoundaryContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(createValue());\nvar useQueryErrorResetBoundary = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryErrorResetBoundaryContext);\nvar QueryErrorResetBoundary = ({ children })=>{\n    const [value] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"QueryErrorResetBoundary.useState\": ()=>createValue()\n    }[\"QueryErrorResetBoundary.useState\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryErrorResetBoundaryContext.Provider, {\n        value,\n        children: typeof children === \"function\" ? children(value) : children\n    });\n};\n //# sourceMappingURL=QueryErrorResetBoundary.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensurePreventErrorBoundaryRetry: () => (/* binding */ ensurePreventErrorBoundaryRetry),\n/* harmony export */   getHasError: () => (/* binding */ getHasError),\n/* harmony export */   useClearResetErrorBoundary: () => (/* binding */ useClearResetErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* __next_internal_client_entry_do_not_use__ ensurePreventErrorBoundaryRetry,getHasError,useClearResetErrorBoundary auto */ // src/errorBoundaryUtils.ts\n\n\nvar ensurePreventErrorBoundaryRetry = (options, errorResetBoundary)=>{\n    if (options.suspense || options.throwOnError || options.experimental_prefetchInRender) {\n        if (!errorResetBoundary.isReset()) {\n            options.retryOnMount = false;\n        }\n    }\n};\nvar useClearResetErrorBoundary = (errorResetBoundary)=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useClearResetErrorBoundary.useEffect\": ()=>{\n            errorResetBoundary.clearReset();\n        }\n    }[\"useClearResetErrorBoundary.useEffect\"], [\n        errorResetBoundary\n    ]);\n};\nvar getHasError = ({ result, errorResetBoundary, throwOnError, query, suspense })=>{\n    return result.isError && !errorResetBoundary.isReset() && !result.isFetching && query && (suspense && result.data === void 0 || (0,_tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__.shouldThrowError)(throwOnError, [\n        result.error,\n        query\n    ]));\n};\n //# sourceMappingURL=errorBoundaryUtils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/infiniteQueryOptions.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/infiniteQueryOptions.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   infiniteQueryOptions: () => (/* binding */ infiniteQueryOptions)\n/* harmony export */ });\n// src/infiniteQueryOptions.ts\nfunction infiniteQueryOptions(options) {\n  return options;\n}\n\n//# sourceMappingURL=infiniteQueryOptions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL21vZGVybi9pbmZpbml0ZVF1ZXJ5T3B0aW9ucy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEVZT1NJXFxEcm9wYm94XFxQQ1xcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxwZXJzb25hbC1qb3VybmFsLWJsb2dcXG5vZGVfbW9kdWxlc1xcQHRhbnN0YWNrXFxyZWFjdC1xdWVyeVxcYnVpbGRcXG1vZGVyblxcaW5maW5pdGVRdWVyeU9wdGlvbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2luZmluaXRlUXVlcnlPcHRpb25zLnRzXG5mdW5jdGlvbiBpbmZpbml0ZVF1ZXJ5T3B0aW9ucyhvcHRpb25zKSB7XG4gIHJldHVybiBvcHRpb25zO1xufVxuZXhwb3J0IHtcbiAgaW5maW5pdGVRdWVyeU9wdGlvbnNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmZpbml0ZVF1ZXJ5T3B0aW9ucy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/infiniteQueryOptions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/queryOptions.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/queryOptions.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryOptions: () => (/* binding */ queryOptions)\n/* harmony export */ });\n// src/queryOptions.ts\nfunction queryOptions(options) {\n  return options;\n}\n\n//# sourceMappingURL=queryOptions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL21vZGVybi9xdWVyeU9wdGlvbnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxFWU9TSVxcRHJvcGJveFxcUENcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xccGVyc29uYWwtam91cm5hbC1ibG9nXFxub2RlX21vZHVsZXNcXEB0YW5zdGFja1xccmVhY3QtcXVlcnlcXGJ1aWxkXFxtb2Rlcm5cXHF1ZXJ5T3B0aW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvcXVlcnlPcHRpb25zLnRzXG5mdW5jdGlvbiBxdWVyeU9wdGlvbnMob3B0aW9ucykge1xuICByZXR1cm4gb3B0aW9ucztcbn1cbmV4cG9ydCB7XG4gIHF1ZXJ5T3B0aW9uc1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXF1ZXJ5T3B0aW9ucy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/queryOptions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/suspense.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/suspense.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultThrowOnError: () => (/* binding */ defaultThrowOnError),\n/* harmony export */   ensureSuspenseTimers: () => (/* binding */ ensureSuspenseTimers),\n/* harmony export */   fetchOptimistic: () => (/* binding */ fetchOptimistic),\n/* harmony export */   shouldSuspend: () => (/* binding */ shouldSuspend),\n/* harmony export */   willFetch: () => (/* binding */ willFetch)\n/* harmony export */ });\n// src/suspense.ts\nvar defaultThrowOnError = (_error, query) => query.state.data === void 0;\nvar ensureSuspenseTimers = (defaultedOptions) => {\n  if (defaultedOptions.suspense) {\n    const MIN_SUSPENSE_TIME_MS = 1e3;\n    const clamp = (value) => value === \"static\" ? value : Math.max(value ?? MIN_SUSPENSE_TIME_MS, MIN_SUSPENSE_TIME_MS);\n    const originalStaleTime = defaultedOptions.staleTime;\n    defaultedOptions.staleTime = typeof originalStaleTime === \"function\" ? (...args) => clamp(originalStaleTime(...args)) : clamp(originalStaleTime);\n    if (typeof defaultedOptions.gcTime === \"number\") {\n      defaultedOptions.gcTime = Math.max(\n        defaultedOptions.gcTime,\n        MIN_SUSPENSE_TIME_MS\n      );\n    }\n  }\n};\nvar willFetch = (result, isRestoring) => result.isLoading && result.isFetching && !isRestoring;\nvar shouldSuspend = (defaultedOptions, result) => defaultedOptions?.suspense && result.isPending;\nvar fetchOptimistic = (defaultedOptions, observer, errorResetBoundary) => observer.fetchOptimistic(defaultedOptions).catch(() => {\n  errorResetBoundary.clearReset();\n});\n\n//# sourceMappingURL=suspense.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/suspense.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBaseQuery: () => (/* binding */ useBaseQuery)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryClientProvider.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _QueryErrorResetBoundary_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QueryErrorResetBoundary.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js\");\n/* harmony import */ var _errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./errorBoundaryUtils.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js\");\n/* harmony import */ var _IsRestoringProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./IsRestoringProvider.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js\");\n/* harmony import */ var _suspense_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./suspense.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/suspense.js\");\n/* __next_internal_client_entry_do_not_use__ useBaseQuery auto */ // src/useBaseQuery.ts\n\n\n\n\n\n\n\nfunction useBaseQuery(options, Observer, queryClient) {\n    if (true) {\n        if (typeof options !== \"object\" || Array.isArray(options)) {\n            throw new Error('Bad argument type. Starting with v5, only the \"Object\" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object');\n        }\n    }\n    const isRestoring = (0,_IsRestoringProvider_js__WEBPACK_IMPORTED_MODULE_1__.useIsRestoring)();\n    const errorResetBoundary = (0,_QueryErrorResetBoundary_js__WEBPACK_IMPORTED_MODULE_2__.useQueryErrorResetBoundary)();\n    const client = (0,_QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)(queryClient);\n    const defaultedOptions = client.defaultQueryOptions(options);\n    client.getDefaultOptions().queries?._experimental_beforeQuery?.(defaultedOptions);\n    if (true) {\n        if (!defaultedOptions.queryFn) {\n            console.error(`[${defaultedOptions.queryHash}]: No queryFn was passed as an option, and no default queryFn was found. The queryFn parameter is only optional when using a default queryFn. More info here: https://tanstack.com/query/latest/docs/framework/react/guides/default-query-function`);\n        }\n    }\n    defaultedOptions._optimisticResults = isRestoring ? \"isRestoring\" : \"optimistic\";\n    (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.ensureSuspenseTimers)(defaultedOptions);\n    (0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.ensurePreventErrorBoundaryRetry)(defaultedOptions, errorResetBoundary);\n    (0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.useClearResetErrorBoundary)(errorResetBoundary);\n    const isNewCacheEntry = !client.getQueryCache().get(defaultedOptions.queryHash);\n    const [observer] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"useBaseQuery.useState\": ()=>new Observer(client, defaultedOptions)\n    }[\"useBaseQuery.useState\"]);\n    const result = observer.getOptimisticResult(defaultedOptions);\n    const shouldSubscribe = !isRestoring && options.subscribed !== false;\n    react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useBaseQuery.useSyncExternalStore.useCallback\": (onStoreChange)=>{\n            const unsubscribe = shouldSubscribe ? observer.subscribe(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batchCalls(onStoreChange)) : _tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__.noop;\n            observer.updateResult();\n            return unsubscribe;\n        }\n    }[\"useBaseQuery.useSyncExternalStore.useCallback\"], [\n        observer,\n        shouldSubscribe\n    ]), {\n        \"useBaseQuery.useSyncExternalStore\": ()=>observer.getCurrentResult()\n    }[\"useBaseQuery.useSyncExternalStore\"], {\n        \"useBaseQuery.useSyncExternalStore\": ()=>observer.getCurrentResult()\n    }[\"useBaseQuery.useSyncExternalStore\"]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useBaseQuery.useEffect\": ()=>{\n            observer.setOptions(defaultedOptions);\n        }\n    }[\"useBaseQuery.useEffect\"], [\n        defaultedOptions,\n        observer\n    ]);\n    if ((0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.shouldSuspend)(defaultedOptions, result)) {\n        throw (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.fetchOptimistic)(defaultedOptions, observer, errorResetBoundary);\n    }\n    if ((0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.getHasError)({\n        result,\n        errorResetBoundary,\n        throwOnError: defaultedOptions.throwOnError,\n        query: client.getQueryCache().get(defaultedOptions.queryHash),\n        suspense: defaultedOptions.suspense\n    })) {\n        throw result.error;\n    }\n    ;\n    client.getDefaultOptions().queries?._experimental_afterQuery?.(defaultedOptions, result);\n    if (defaultedOptions.experimental_prefetchInRender && !_tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__.isServer && (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.willFetch)(result, isRestoring)) {\n        const promise = isNewCacheEntry ? // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted\n        (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.fetchOptimistic)(defaultedOptions, observer, errorResetBoundary) : // subscribe to the \"cache promise\" so that we can finalize the currentThenable once data comes in\n        client.getQueryCache().get(defaultedOptions.queryHash)?.promise;\n        promise?.catch(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__.noop).finally(()=>{\n            observer.updateResult();\n        });\n    }\n    return !defaultedOptions.notifyOnChangeProps ? observer.trackResult(result) : result;\n}\n //# sourceMappingURL=useBaseQuery.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInfiniteQuery: () => (/* binding */ useInfiniteQuery)\n/* harmony export */ });\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryObserver.js\");\n/* harmony import */ var _useBaseQuery_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useBaseQuery.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js\");\n/* __next_internal_client_entry_do_not_use__ useInfiniteQuery auto */ // src/useInfiniteQuery.ts\n\n\nfunction useInfiniteQuery(options, queryClient) {\n    return (0,_useBaseQuery_js__WEBPACK_IMPORTED_MODULE_0__.useBaseQuery)(options, _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__.InfiniteQueryObserver, queryClient);\n}\n //# sourceMappingURL=useInfiniteQuery.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useMutation.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMutation: () => (/* binding */ useMutation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationObserver.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* __next_internal_client_entry_do_not_use__ useMutation auto */ // src/useMutation.ts\n\n\n\nfunction useMutation(options, queryClient) {\n    const client = (0,_QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)(queryClient);\n    const [observer] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"useMutation.useState\": ()=>new _tanstack_query_core__WEBPACK_IMPORTED_MODULE_2__.MutationObserver(client, options)\n    }[\"useMutation.useState\"]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useMutation.useEffect\": ()=>{\n            observer.setOptions(options);\n        }\n    }[\"useMutation.useEffect\"], [\n        observer,\n        options\n    ]);\n    const result = react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useMutation.useSyncExternalStore[result]\": (onStoreChange)=>observer.subscribe(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(onStoreChange))\n    }[\"useMutation.useSyncExternalStore[result]\"], [\n        observer\n    ]), {\n        \"useMutation.useSyncExternalStore[result]\": ()=>observer.getCurrentResult()\n    }[\"useMutation.useSyncExternalStore[result]\"], {\n        \"useMutation.useSyncExternalStore[result]\": ()=>observer.getCurrentResult()\n    }[\"useMutation.useSyncExternalStore[result]\"]);\n    const mutate = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useMutation.useCallback[mutate]\": (variables, mutateOptions)=>{\n            observer.mutate(variables, mutateOptions).catch(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_4__.noop);\n        }\n    }[\"useMutation.useCallback[mutate]\"], [\n        observer\n    ]);\n    if (result.error && (0,_tanstack_query_core__WEBPACK_IMPORTED_MODULE_4__.shouldThrowError)(observer.options.throwOnError, [\n        result.error\n    ])) {\n        throw result.error;\n    }\n    return {\n        ...result,\n        mutate,\n        mutateAsync: result.mutate\n    };\n}\n //# sourceMappingURL=useMutation.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL21vZGVybi91c2VNdXRhdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQ3VCO0FBTWhCO0FBQ3dCO0FBVXhCLFNBQVMsWUFNZCxTQUNBLGFBQytEO0lBQy9ELE1BQU0sU0FBUyx1RUFBYyxDQUFDLFdBQVc7SUFFekMsTUFBTSxDQUFDLFFBQVEsSUFBVTtnQ0FDdkIsSUFDRSxJQUFJLGtFQUFnQixDQUNsQixRQUNBOztJQUlBO2lDQUFVO1lBQ2QsU0FBUyxXQUFXLE9BQU87UUFDN0I7Z0NBQUc7UUFBQztRQUFVLE9BQU87S0FBQztJQUV0QixNQUFNLFNBQWUsd0RBQ2I7b0RBQ0osQ0FBQyxnQkFDQyxTQUFTLFVBQVUsK0RBQWEsQ0FBQyxXQUFXLGFBQWEsQ0FBQzttREFDNUQ7UUFBQyxRQUFRO0tBQUE7b0RBRVgsSUFBTSxTQUFTLGlCQUFpQjs7b0RBQ2hDLElBQU0sU0FBUyxpQkFBaUI7O0lBR2xDLE1BQU0sU0FBZTsyQ0FHbkIsQ0FBQyxXQUFXO1lBQ1YsU0FBUyxPQUFPLFdBQVcsYUFBYSxFQUFFLE1BQU0sc0RBQUk7UUFDdEQ7MENBQ0E7UUFBQyxRQUFRO0tBQUE7SUFHWCxJQUNFLE9BQU8sU0FDUCxzRUFBZ0IsQ0FBQyxTQUFTLFFBQVEsY0FBYztRQUFDLE9BQU8sS0FBSztLQUFDLEdBQzlEO1FBQ0EsTUFBTSxPQUFPO0lBQ2Y7SUFFQSxPQUFPO1FBQUUsR0FBRztRQUFRO1FBQVEsYUFBYSxPQUFPO0lBQU87QUFDekQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRVlPU0lcXERyb3Bib3hcXFBDXFxEb2N1bWVudHNcXHNyY1xcdXNlTXV0YXRpb24udHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCB7XG4gIE11dGF0aW9uT2JzZXJ2ZXIsXG4gIG5vb3AsXG4gIG5vdGlmeU1hbmFnZXIsXG4gIHNob3VsZFRocm93RXJyb3IsXG59IGZyb20gJ0B0YW5zdGFjay9xdWVyeS1jb3JlJ1xuaW1wb3J0IHsgdXNlUXVlcnlDbGllbnQgfSBmcm9tICcuL1F1ZXJ5Q2xpZW50UHJvdmlkZXInXG5pbXBvcnQgdHlwZSB7XG4gIFVzZU11dGF0ZUZ1bmN0aW9uLFxuICBVc2VNdXRhdGlvbk9wdGlvbnMsXG4gIFVzZU11dGF0aW9uUmVzdWx0LFxufSBmcm9tICcuL3R5cGVzJ1xuaW1wb3J0IHR5cGUgeyBEZWZhdWx0RXJyb3IsIFF1ZXJ5Q2xpZW50IH0gZnJvbSAnQHRhbnN0YWNrL3F1ZXJ5LWNvcmUnXG5cbi8vIEhPT0tcblxuZXhwb3J0IGZ1bmN0aW9uIHVzZU11dGF0aW9uPFxuICBURGF0YSA9IHVua25vd24sXG4gIFRFcnJvciA9IERlZmF1bHRFcnJvcixcbiAgVFZhcmlhYmxlcyA9IHZvaWQsXG4gIFRPbk11dGF0ZVJlc3VsdCA9IHVua25vd24sXG4+KFxuICBvcHRpb25zOiBVc2VNdXRhdGlvbk9wdGlvbnM8VERhdGEsIFRFcnJvciwgVFZhcmlhYmxlcywgVE9uTXV0YXRlUmVzdWx0PixcbiAgcXVlcnlDbGllbnQ/OiBRdWVyeUNsaWVudCxcbik6IFVzZU11dGF0aW9uUmVzdWx0PFREYXRhLCBURXJyb3IsIFRWYXJpYWJsZXMsIFRPbk11dGF0ZVJlc3VsdD4ge1xuICBjb25zdCBjbGllbnQgPSB1c2VRdWVyeUNsaWVudChxdWVyeUNsaWVudClcblxuICBjb25zdCBbb2JzZXJ2ZXJdID0gUmVhY3QudXNlU3RhdGUoXG4gICAgKCkgPT5cbiAgICAgIG5ldyBNdXRhdGlvbk9ic2VydmVyPFREYXRhLCBURXJyb3IsIFRWYXJpYWJsZXMsIFRPbk11dGF0ZVJlc3VsdD4oXG4gICAgICAgIGNsaWVudCxcbiAgICAgICAgb3B0aW9ucyxcbiAgICAgICksXG4gIClcblxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIG9ic2VydmVyLnNldE9wdGlvbnMob3B0aW9ucylcbiAgfSwgW29ic2VydmVyLCBvcHRpb25zXSlcblxuICBjb25zdCByZXN1bHQgPSBSZWFjdC51c2VTeW5jRXh0ZXJuYWxTdG9yZShcbiAgICBSZWFjdC51c2VDYWxsYmFjayhcbiAgICAgIChvblN0b3JlQ2hhbmdlKSA9PlxuICAgICAgICBvYnNlcnZlci5zdWJzY3JpYmUobm90aWZ5TWFuYWdlci5iYXRjaENhbGxzKG9uU3RvcmVDaGFuZ2UpKSxcbiAgICAgIFtvYnNlcnZlcl0sXG4gICAgKSxcbiAgICAoKSA9PiBvYnNlcnZlci5nZXRDdXJyZW50UmVzdWx0KCksXG4gICAgKCkgPT4gb2JzZXJ2ZXIuZ2V0Q3VycmVudFJlc3VsdCgpLFxuICApXG5cbiAgY29uc3QgbXV0YXRlID0gUmVhY3QudXNlQ2FsbGJhY2s8XG4gICAgVXNlTXV0YXRlRnVuY3Rpb248VERhdGEsIFRFcnJvciwgVFZhcmlhYmxlcywgVE9uTXV0YXRlUmVzdWx0PlxuICA+KFxuICAgICh2YXJpYWJsZXMsIG11dGF0ZU9wdGlvbnMpID0+IHtcbiAgICAgIG9ic2VydmVyLm11dGF0ZSh2YXJpYWJsZXMsIG11dGF0ZU9wdGlvbnMpLmNhdGNoKG5vb3ApXG4gICAgfSxcbiAgICBbb2JzZXJ2ZXJdLFxuICApXG5cbiAgaWYgKFxuICAgIHJlc3VsdC5lcnJvciAmJlxuICAgIHNob3VsZFRocm93RXJyb3Iob2JzZXJ2ZXIub3B0aW9ucy50aHJvd09uRXJyb3IsIFtyZXN1bHQuZXJyb3JdKVxuICApIHtcbiAgICB0aHJvdyByZXN1bHQuZXJyb3JcbiAgfVxuXG4gIHJldHVybiB7IC4uLnJlc3VsdCwgbXV0YXRlLCBtdXRhdGVBc3luYzogcmVzdWx0Lm11dGF0ZSB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/usePrefetchInfiniteQuery.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/usePrefetchInfiniteQuery.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePrefetchInfiniteQuery: () => (/* binding */ usePrefetchInfiniteQuery)\n/* harmony export */ });\n/* harmony import */ var _QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./QueryClientProvider.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n// src/usePrefetchInfiniteQuery.tsx\n\nfunction usePrefetchInfiniteQuery(options, queryClient) {\n  const client = (0,_QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)(queryClient);\n  if (!client.getQueryState(options.queryKey)) {\n    client.prefetchInfiniteQuery(options);\n  }\n}\n\n//# sourceMappingURL=usePrefetchInfiniteQuery.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL21vZGVybi91c2VQcmVmZXRjaEluZmluaXRlUXVlcnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMwRDtBQUMxRDtBQUNBLGlCQUFpQix1RUFBYztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRVlPU0lcXERyb3Bib3hcXFBDXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXHBlcnNvbmFsLWpvdXJuYWwtYmxvZ1xcbm9kZV9tb2R1bGVzXFxAdGFuc3RhY2tcXHJlYWN0LXF1ZXJ5XFxidWlsZFxcbW9kZXJuXFx1c2VQcmVmZXRjaEluZmluaXRlUXVlcnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3VzZVByZWZldGNoSW5maW5pdGVRdWVyeS50c3hcbmltcG9ydCB7IHVzZVF1ZXJ5Q2xpZW50IH0gZnJvbSBcIi4vUXVlcnlDbGllbnRQcm92aWRlci5qc1wiO1xuZnVuY3Rpb24gdXNlUHJlZmV0Y2hJbmZpbml0ZVF1ZXJ5KG9wdGlvbnMsIHF1ZXJ5Q2xpZW50KSB7XG4gIGNvbnN0IGNsaWVudCA9IHVzZVF1ZXJ5Q2xpZW50KHF1ZXJ5Q2xpZW50KTtcbiAgaWYgKCFjbGllbnQuZ2V0UXVlcnlTdGF0ZShvcHRpb25zLnF1ZXJ5S2V5KSkge1xuICAgIGNsaWVudC5wcmVmZXRjaEluZmluaXRlUXVlcnkob3B0aW9ucyk7XG4gIH1cbn1cbmV4cG9ydCB7XG4gIHVzZVByZWZldGNoSW5maW5pdGVRdWVyeVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXVzZVByZWZldGNoSW5maW5pdGVRdWVyeS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/usePrefetchInfiniteQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/usePrefetchQuery.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/usePrefetchQuery.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePrefetchQuery: () => (/* binding */ usePrefetchQuery)\n/* harmony export */ });\n/* harmony import */ var _QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./QueryClientProvider.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n// src/usePrefetchQuery.tsx\n\nfunction usePrefetchQuery(options, queryClient) {\n  const client = (0,_QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)(queryClient);\n  if (!client.getQueryState(options.queryKey)) {\n    client.prefetchQuery(options);\n  }\n}\n\n//# sourceMappingURL=usePrefetchQuery.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL21vZGVybi91c2VQcmVmZXRjaFF1ZXJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDMEQ7QUFDMUQ7QUFDQSxpQkFBaUIsdUVBQWM7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEVZT1NJXFxEcm9wYm94XFxQQ1xcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxwZXJzb25hbC1qb3VybmFsLWJsb2dcXG5vZGVfbW9kdWxlc1xcQHRhbnN0YWNrXFxyZWFjdC1xdWVyeVxcYnVpbGRcXG1vZGVyblxcdXNlUHJlZmV0Y2hRdWVyeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdXNlUHJlZmV0Y2hRdWVyeS50c3hcbmltcG9ydCB7IHVzZVF1ZXJ5Q2xpZW50IH0gZnJvbSBcIi4vUXVlcnlDbGllbnRQcm92aWRlci5qc1wiO1xuZnVuY3Rpb24gdXNlUHJlZmV0Y2hRdWVyeShvcHRpb25zLCBxdWVyeUNsaWVudCkge1xuICBjb25zdCBjbGllbnQgPSB1c2VRdWVyeUNsaWVudChxdWVyeUNsaWVudCk7XG4gIGlmICghY2xpZW50LmdldFF1ZXJ5U3RhdGUob3B0aW9ucy5xdWVyeUtleSkpIHtcbiAgICBjbGllbnQucHJlZmV0Y2hRdWVyeShvcHRpb25zKTtcbiAgfVxufVxuZXhwb3J0IHtcbiAgdXNlUHJlZmV0Y2hRdWVyeVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXVzZVByZWZldGNoUXVlcnkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/usePrefetchQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/useQueries.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useQueries.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQueries: () => (/* binding */ useQueries)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queriesObserver.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js\");\n/* harmony import */ var _QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _IsRestoringProvider_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./IsRestoringProvider.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js\");\n/* harmony import */ var _QueryErrorResetBoundary_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryErrorResetBoundary.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js\");\n/* harmony import */ var _errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./errorBoundaryUtils.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js\");\n/* harmony import */ var _suspense_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./suspense.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/suspense.js\");\n/* __next_internal_client_entry_do_not_use__ useQueries auto */ // src/useQueries.ts\n\n\n\n\n\n\n\nfunction useQueries({ queries, ...options }, queryClient) {\n    const client = (0,_QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)(queryClient);\n    const isRestoring = (0,_IsRestoringProvider_js__WEBPACK_IMPORTED_MODULE_2__.useIsRestoring)();\n    const errorResetBoundary = (0,_QueryErrorResetBoundary_js__WEBPACK_IMPORTED_MODULE_3__.useQueryErrorResetBoundary)();\n    const defaultedQueries = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"useQueries.useMemo[defaultedQueries]\": ()=>queries.map({\n                \"useQueries.useMemo[defaultedQueries]\": (opts)=>{\n                    const defaultedOptions = client.defaultQueryOptions(opts);\n                    defaultedOptions._optimisticResults = isRestoring ? \"isRestoring\" : \"optimistic\";\n                    return defaultedOptions;\n                }\n            }[\"useQueries.useMemo[defaultedQueries]\"])\n    }[\"useQueries.useMemo[defaultedQueries]\"], [\n        queries,\n        client,\n        isRestoring\n    ]);\n    defaultedQueries.forEach((query)=>{\n        (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.ensureSuspenseTimers)(query);\n        (0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.ensurePreventErrorBoundaryRetry)(query, errorResetBoundary);\n    });\n    (0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.useClearResetErrorBoundary)(errorResetBoundary);\n    const [observer] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"useQueries.useState\": ()=>new _tanstack_query_core__WEBPACK_IMPORTED_MODULE_6__.QueriesObserver(client, defaultedQueries, options)\n    }[\"useQueries.useState\"]);\n    const [optimisticResult, getCombinedResult, trackResult] = observer.getOptimisticResult(defaultedQueries, options.combine);\n    const shouldSubscribe = !isRestoring && options.subscribed !== false;\n    react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useQueries.useSyncExternalStore.useCallback\": (onStoreChange)=>shouldSubscribe ? observer.subscribe(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__.notifyManager.batchCalls(onStoreChange)) : _tanstack_query_core__WEBPACK_IMPORTED_MODULE_8__.noop\n    }[\"useQueries.useSyncExternalStore.useCallback\"], [\n        observer,\n        shouldSubscribe\n    ]), {\n        \"useQueries.useSyncExternalStore\": ()=>observer.getCurrentResult()\n    }[\"useQueries.useSyncExternalStore\"], {\n        \"useQueries.useSyncExternalStore\": ()=>observer.getCurrentResult()\n    }[\"useQueries.useSyncExternalStore\"]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useQueries.useEffect\": ()=>{\n            observer.setQueries(defaultedQueries, options);\n        }\n    }[\"useQueries.useEffect\"], [\n        defaultedQueries,\n        options,\n        observer\n    ]);\n    const shouldAtLeastOneSuspend = optimisticResult.some((result, index)=>(0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.shouldSuspend)(defaultedQueries[index], result));\n    const suspensePromises = shouldAtLeastOneSuspend ? optimisticResult.flatMap((result, index)=>{\n        const opts = defaultedQueries[index];\n        if (opts) {\n            const queryObserver = new _tanstack_query_core__WEBPACK_IMPORTED_MODULE_9__.QueryObserver(client, opts);\n            if ((0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.shouldSuspend)(opts, result)) {\n                return (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.fetchOptimistic)(opts, queryObserver, errorResetBoundary);\n            } else if ((0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.willFetch)(result, isRestoring)) {\n                void (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.fetchOptimistic)(opts, queryObserver, errorResetBoundary);\n            }\n        }\n        return [];\n    }) : [];\n    if (suspensePromises.length > 0) {\n        throw Promise.all(suspensePromises);\n    }\n    const firstSingleResultWhichShouldThrow = optimisticResult.find((result, index)=>{\n        const query = defaultedQueries[index];\n        return query && (0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.getHasError)({\n            result,\n            errorResetBoundary,\n            throwOnError: query.throwOnError,\n            query: client.getQueryCache().get(query.queryHash),\n            suspense: query.suspense\n        });\n    });\n    if (firstSingleResultWhichShouldThrow?.error) {\n        throw firstSingleResultWhichShouldThrow.error;\n    }\n    return getCombinedResult(trackResult());\n}\n //# sourceMappingURL=useQueries.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/useQueries.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useQuery.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQuery: () => (/* binding */ useQuery)\n/* harmony export */ });\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js\");\n/* harmony import */ var _useBaseQuery_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useBaseQuery.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js\");\n/* __next_internal_client_entry_do_not_use__ useQuery auto */ // src/useQuery.ts\n\n\nfunction useQuery(options, queryClient) {\n    return (0,_useBaseQuery_js__WEBPACK_IMPORTED_MODULE_0__.useBaseQuery)(options, _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__.QueryObserver, queryClient);\n}\n //# sourceMappingURL=useQuery.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/useSuspenseInfiniteQuery.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useSuspenseInfiniteQuery.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSuspenseInfiniteQuery: () => (/* binding */ useSuspenseInfiniteQuery)\n/* harmony export */ });\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryObserver.js\");\n/* harmony import */ var _useBaseQuery_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useBaseQuery.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js\");\n/* harmony import */ var _suspense_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./suspense.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/suspense.js\");\n/* __next_internal_client_entry_do_not_use__ useSuspenseInfiniteQuery auto */ // src/useSuspenseInfiniteQuery.ts\n\n\n\nfunction useSuspenseInfiniteQuery(options, queryClient) {\n    if (true) {\n        if (options.queryFn === _tanstack_query_core__WEBPACK_IMPORTED_MODULE_0__.skipToken) {\n            console.error(\"skipToken is not allowed for useSuspenseInfiniteQuery\");\n        }\n    }\n    return (0,_useBaseQuery_js__WEBPACK_IMPORTED_MODULE_1__.useBaseQuery)({\n        ...options,\n        enabled: true,\n        suspense: true,\n        throwOnError: _suspense_js__WEBPACK_IMPORTED_MODULE_2__.defaultThrowOnError\n    }, _tanstack_query_core__WEBPACK_IMPORTED_MODULE_3__.InfiniteQueryObserver, queryClient);\n}\n //# sourceMappingURL=useSuspenseInfiniteQuery.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL21vZGVybi91c2VTdXNwZW5zZUluZmluaXRlUXVlcnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ2lEO0FBQ3BCO0FBQ087QUFjN0IsU0FBUyx5QkFPZCxTQU9BLGFBQytDO0lBQy9DLElBQUksSUFBeUIsRUFBYztRQUN6QyxJQUFLLFFBQVEsWUFBb0IsMkRBQVMsRUFBRTtZQUMxQyxRQUFRLE1BQU0sdURBQXVEO1FBQ3ZFO0lBQ0Y7SUFFQSxPQUFPLDhEQUFZLENBQ2pCO1FBQ0UsR0FBRztRQUNILFNBQVM7UUFDVCxVQUFVO1FBQ1YsY0FBYyw2REFBbUI7SUFDbkMsR0FDQSx1RUFBcUIsRUFDckI7QUFFSiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxFWU9TSVxcRHJvcGJveFxcUENcXERvY3VtZW50c1xcc3JjXFx1c2VTdXNwZW5zZUluZmluaXRlUXVlcnkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5pbXBvcnQgeyBJbmZpbml0ZVF1ZXJ5T2JzZXJ2ZXIsIHNraXBUb2tlbiB9IGZyb20gJ0B0YW5zdGFjay9xdWVyeS1jb3JlJ1xuaW1wb3J0IHsgdXNlQmFzZVF1ZXJ5IH0gZnJvbSAnLi91c2VCYXNlUXVlcnknXG5pbXBvcnQgeyBkZWZhdWx0VGhyb3dPbkVycm9yIH0gZnJvbSAnLi9zdXNwZW5zZSdcbmltcG9ydCB0eXBlIHtcbiAgRGVmYXVsdEVycm9yLFxuICBJbmZpbml0ZURhdGEsXG4gIEluZmluaXRlUXVlcnlPYnNlcnZlclN1Y2Nlc3NSZXN1bHQsXG4gIFF1ZXJ5Q2xpZW50LFxuICBRdWVyeUtleSxcbiAgUXVlcnlPYnNlcnZlcixcbn0gZnJvbSAnQHRhbnN0YWNrL3F1ZXJ5LWNvcmUnXG5pbXBvcnQgdHlwZSB7XG4gIFVzZVN1c3BlbnNlSW5maW5pdGVRdWVyeU9wdGlvbnMsXG4gIFVzZVN1c3BlbnNlSW5maW5pdGVRdWVyeVJlc3VsdCxcbn0gZnJvbSAnLi90eXBlcydcblxuZXhwb3J0IGZ1bmN0aW9uIHVzZVN1c3BlbnNlSW5maW5pdGVRdWVyeTxcbiAgVFF1ZXJ5Rm5EYXRhLFxuICBURXJyb3IgPSBEZWZhdWx0RXJyb3IsXG4gIFREYXRhID0gSW5maW5pdGVEYXRhPFRRdWVyeUZuRGF0YT4sXG4gIFRRdWVyeUtleSBleHRlbmRzIFF1ZXJ5S2V5ID0gUXVlcnlLZXksXG4gIFRQYWdlUGFyYW0gPSB1bmtub3duLFxuPihcbiAgb3B0aW9uczogVXNlU3VzcGVuc2VJbmZpbml0ZVF1ZXJ5T3B0aW9uczxcbiAgICBUUXVlcnlGbkRhdGEsXG4gICAgVEVycm9yLFxuICAgIFREYXRhLFxuICAgIFRRdWVyeUtleSxcbiAgICBUUGFnZVBhcmFtXG4gID4sXG4gIHF1ZXJ5Q2xpZW50PzogUXVlcnlDbGllbnQsXG4pOiBVc2VTdXNwZW5zZUluZmluaXRlUXVlcnlSZXN1bHQ8VERhdGEsIFRFcnJvcj4ge1xuICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICAgIGlmICgob3B0aW9ucy5xdWVyeUZuIGFzIGFueSkgPT09IHNraXBUb2tlbikge1xuICAgICAgY29uc29sZS5lcnJvcignc2tpcFRva2VuIGlzIG5vdCBhbGxvd2VkIGZvciB1c2VTdXNwZW5zZUluZmluaXRlUXVlcnknKVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiB1c2VCYXNlUXVlcnkoXG4gICAge1xuICAgICAgLi4ub3B0aW9ucyxcbiAgICAgIGVuYWJsZWQ6IHRydWUsXG4gICAgICBzdXNwZW5zZTogdHJ1ZSxcbiAgICAgIHRocm93T25FcnJvcjogZGVmYXVsdFRocm93T25FcnJvcixcbiAgICB9LFxuICAgIEluZmluaXRlUXVlcnlPYnNlcnZlciBhcyB0eXBlb2YgUXVlcnlPYnNlcnZlcixcbiAgICBxdWVyeUNsaWVudCxcbiAgKSBhcyBJbmZpbml0ZVF1ZXJ5T2JzZXJ2ZXJTdWNjZXNzUmVzdWx0PFREYXRhLCBURXJyb3I+XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/useSuspenseInfiniteQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQueries.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useSuspenseQueries.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSuspenseQueries: () => (/* binding */ useSuspenseQueries)\n/* harmony export */ });\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _useQueries_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useQueries.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQueries.js\");\n/* harmony import */ var _suspense_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./suspense.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/suspense.js\");\n/* __next_internal_client_entry_do_not_use__ useSuspenseQueries auto */ // src/useSuspenseQueries.ts\n\n\n\nfunction useSuspenseQueries(options, queryClient) {\n    return (0,_useQueries_js__WEBPACK_IMPORTED_MODULE_0__.useQueries)({\n        ...options,\n        queries: options.queries.map({\n            \"useSuspenseQueries.useQueries\": (query)=>{\n                if (true) {\n                    if (query.queryFn === _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__.skipToken) {\n                        console.error(\"skipToken is not allowed for useSuspenseQueries\");\n                    }\n                }\n                return {\n                    ...query,\n                    suspense: true,\n                    throwOnError: _suspense_js__WEBPACK_IMPORTED_MODULE_2__.defaultThrowOnError,\n                    enabled: true,\n                    placeholderData: void 0\n                };\n            }\n        }[\"useSuspenseQueries.useQueries\"])\n    }, queryClient);\n}\n //# sourceMappingURL=useSuspenseQueries.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQueries.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQuery.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useSuspenseQuery.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSuspenseQuery: () => (/* binding */ useSuspenseQuery)\n/* harmony export */ });\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js\");\n/* harmony import */ var _useBaseQuery_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useBaseQuery.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js\");\n/* harmony import */ var _suspense_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./suspense.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/suspense.js\");\n/* __next_internal_client_entry_do_not_use__ useSuspenseQuery auto */ // src/useSuspenseQuery.ts\n\n\n\nfunction useSuspenseQuery(options, queryClient) {\n    if (true) {\n        if (options.queryFn === _tanstack_query_core__WEBPACK_IMPORTED_MODULE_0__.skipToken) {\n            console.error(\"skipToken is not allowed for useSuspenseQuery\");\n        }\n    }\n    return (0,_useBaseQuery_js__WEBPACK_IMPORTED_MODULE_1__.useBaseQuery)({\n        ...options,\n        enabled: true,\n        suspense: true,\n        throwOnError: _suspense_js__WEBPACK_IMPORTED_MODULE_2__.defaultThrowOnError,\n        placeholderData: void 0\n    }, _tanstack_query_core__WEBPACK_IMPORTED_MODULE_3__.QueryObserver, queryClient);\n}\n //# sourceMappingURL=useSuspenseQuery.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQuery.js\n");

/***/ })

};
;